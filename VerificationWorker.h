﻿#ifndef VerificationWorker_H
#define VerificationWorker_H

#include <QObject>
#include <QVector>
#include <QPair>
#include <QByteArray>
#include <QDataStream>
#include <QTimer>
#include <QEventLoop>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QDebug>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonDocument>

#include "ProjectDataTypes.h"
#include "WorkerConfig.h"

class VerificationWorker : public QObject
{
    Q_OBJECT

public:
    explicit VerificationWorker(QObject *parent = nullptr);
    ~VerificationWorker();

    // 设置设备配置
    void setDeviceConfig(const AdjDeviceConfig &config);

    // 设置命令发送函数指针
    typedef QPair<bool, QString> (*CommandHandlerFunc)(const QByteArray &, const QString &);
    void setCommandHandlers(CommandHandlerFunc calHandler, CommandHandlerFunc deviceHandler);

public slots:
    // 开始校准过程
    void startCalibration();
    // 中止校准过程
    void abortVerification();
    // 单通道复校过程
    void restartCalibration(int groupIndex, double referenceValue, int channel);

    // 接收更新UI返回结果
    void uiUpdateCompleted();

    // 设置是否为批量模式
    void setBatchMode(bool isBatchMode);

signals:
    // 通知UI校准开始
    void calibrationStarted();
    // 通知UI校准完成
    void calibrationFinished(bool success);
    // 更新校准进度
    void calibrationProgress(int channel, int progress);
    // 记录日志消息
    void logMessage(const QString &message);
    // 更新通道数据
    void updateChannelData(double referenceValue, int channel, QVector<double> resistances, double measuredResistance,
                           double deviationFromReference, double allowedDeviation, double equivalentTempDeviation, bool calibrationResult);
    // 等待更新结果

    // 新增信号，用于传递标定结果到主线程
    void saveResultsRequested();

    void uiUpdateCompletedSignal();

private: // 辅助函数
    bool is_channel_passed(const QVector<double> &deviations);
    double read_resistance(int channel);
    QPair<bool, QVector<double>> read_resistances(int channel, int attempt);
    double calculateAllowedDev(const QString &deviceModel, double refResValue);
    double calculateAllowedDevForTM18ND(const QString &deviceModel, double refResValue); // TM18ND专用允许偏差计算（返回欧姆单位）
    double calculateTempDev(const QString &deviceModel, double refResValue, double devResValue);
    double calculateS(double refResValue, bool isKOhm, const QString &deviceModel);

    // Modbus命令生成函数
    QByteArray createModbusReadFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, int numChannels, FloatPrecision precision = FloatPrecision::Double);
    QByteArray createModbusWriteFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, double ref_value, FloatPrecision precision = FloatPrecision::Double);
    QByteArray createModbusOpen1220Frame(int referChannel, int calChannel);
    QByteArray createModbusCommand(const QString &hexCommand);

    // 发送命令
    QPair<bool, QString> sendCommand(const QByteArray &command, const QString &commandType);

    // 计算CRC
    uint16_t calculateCRC16(const QByteArray &data);

    // TCP协议支持函数
    bool isTcpDevice(const QString &deviceModel);
    QByteArray createModbusTcpFrame(const QByteArray &rtuData);
    QByteArray createModbusRtuFrame(const QString &hexCommand);

    // 1220通道开关专用命令（始终使用RTU协议）
    QByteArray createModbus1220Command(const QString &hexCommand);

    // 成员变量
    AdjDeviceConfig m_deviceConfig;
    bool m_abortRequested;
    bool m_isBatchMode; // 标识是否在批量模式下运行

    // 命令处理函数指针
    CommandHandlerFunc m_calCommandHandler;
    CommandHandlerFunc m_deviceCommandHandler;

    int m_currentRow;

    // Add a member variable to track UI update status
    bool waitForUiUpdate(int timeoutMs = 1000);

    // 根据设备型号确定浮点精度
    FloatPrecision getDevicePrecision() const;

    // 可中断的等待方法
    bool interruptibleSleep(int seconds);
    bool interruptibleMSleep(int milliseconds);

private slots:
};

#endif // VerificationWorker_H
