#ifndef VOLTAGECALIBRATIONWORKER_H
#define VOLTAGECALIBRATIONWORKER_H

#include <QObject>
#include <QThread>
#include <QMessageBox>
#include <QDebug>
#include <QTimer>
#include "WorkerConfig.h"

class VoltageCalibrationWorker : public QObject
{
    Q_OBJECT

public:
    explicit VoltageCalibrationWorker(QObject *parent = nullptr);
    ~VoltageCalibrationWorker();

    void setDeviceConfig(const CalDeviceConfig &config);
    void requestAbort();

    // 设置命令发送函数指针
    typedef QPair<bool, QString> (*CommandHandlerFunc)(const QByteArray &, const QString &);
    void setCommandHandlers(CommandHandlerFunc calHandler);

    // 获取当前校准档位
    int getCurrentLevel() const { return m_currentLevel; }

public slots:
    void startVoltageCalibration();
    void onUserPromptResult(bool confirmed);

signals:
    void logMessage(const QString &message);
    void voltageCalibrationFinished(bool success);
    void voltageCalibrationProgress(int currentStep, int totalSteps);
    void updateVoltageData(double voltage, double deviation, bool passed);
    void voltageLevelCompleted(int level, double finalVoltage, double finalDeviation, bool levelPassed);
    void requestUserPrompt(const QString &message);
    void clearVoltageTable();

private slots:

private:
    struct VoltageLevel
    {
        double targetVoltage; // 目标电压值 (mV)
        QString description;  // 描述文本
    };

    CalDeviceConfig m_deviceConfig;
    bool m_abortRequested;
    QTimer *m_dataCollectionTimer;

    // 命令处理函数指针
    CommandHandlerFunc m_calCommandHandler;

    // 校准状态
    int m_currentLevel;                // 当前校准档位 (0=0mV, 1=50mV)
    int m_currentAttempt;              // 当前尝试次数
    int m_currentRound;                // 当前读取轮次
    QVector<double> m_voltageReadings; // 电压读取值
    QVector<double> m_deviations;      // 偏差值

    static const QVector<VoltageLevel> VOLTAGE_LEVELS;
    static const int MAX_ATTEMPTS = 3;
    static const int MAX_ROUNDS = 10;
    static const double VOLTAGE_THRESHOLD; // 10μV精度要求

    // 私有方法
    void showUserPrompt(double targetVoltage);
    bool writeVoltageCalibrationValue(double voltage);
    void startDataCollection();
    void collectVoltageData();
    bool isLevelPassed(const QVector<double> &deviations);
    void moveToNextLevel();
    void finishCalibration(bool success);

    // 通信相关
    QPair<bool, QString> sendCommand(const QByteArray &command, const QString &commandId);
    QByteArray createModbusCommand(const QString &hexString);
    QByteArray createModbusWriteFrame(quint8 deviceAddr, quint16 startAddr, int channel, double value);
    QByteArray createModbusReadFrame(quint8 deviceAddr, quint16 startAddr, int channel, int count);
    double readVoltageValue();
    uint16_t calculateCRC16(const QByteArray &data);

    // TCP协议支持函数
    bool isTcpDevice(const QString &deviceModel);
    QByteArray createModbusTcpFrame(const QByteArray &rtuData);
    QByteArray createModbusRtuFrame(const QString &hexCommand);

    // 地址配置
    quint16 getWriteAddress() const;
    quint16 getReadAddress() const;
};

#endif // VOLTAGECALIBRATIONWORKER_H
