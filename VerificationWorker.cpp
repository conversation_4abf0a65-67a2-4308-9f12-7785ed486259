#include "VerificationWorker.h"
#include <QApplication>

VerificationWorker::VerificationWorker(QObject *parent)
    : QObject(parent), m_abortRequested(false), m_isBatchMode(false),
      m_calCommandHandler(nullptr), m_deviceCommandHandler(nullptr)
{
}

VerificationWorker::~VerificationWorker()
{
    m_abortRequested = true;
}

void VerificationWorker::setDeviceConfig(const AdjDeviceConfig &config)
{
    m_deviceConfig = config;
}

void VerificationWorker::setCommandHandlers(CommandHandlerFunc calHandler, CommandHandlerFunc deviceHandler)
{
    m_calCommandHandler = calHandler;
    m_deviceCommandHandler = deviceHandler;
}

void VerificationWorker::setBatchMode(bool isBatchMode)
{
    m_isBatchMode = isBatchMode;
}

void VerificationWorker::restartCalibration(int groupIndex, double referenceValue, int channel)
{
    // 检查命令处理函数是否已设置
    if (!m_calCommandHandler || !m_deviceCommandHandler)
    {
        emit logMessage("错误: 命令处理函数未设置");
        emit calibrationFinished(false);
        return;
    }

    emit logMessage(QString("开始 %1 通道 %2 参考阻值 %3 复校过程...").arg(m_deviceConfig.name).arg(channel).arg(referenceValue));
    m_abortRequested = false;

    // 关闭所有通道（初始化状态）
    QByteArray closeSwitchs = createModbusCommand("01 10 00 03 00 02 04 00 00 00 00");
    auto closeResult = sendCommand(closeSwitchs, "1220_ControlSwitch");
    if (!closeResult.first)
    {
        emit logMessage("错误: 初始化关闭所有通道失败：" + closeResult.second);
        emit calibrationFinished(false);
        return;
    }

    // 使用可中断等待，提高中止响应性
    if (!interruptibleSleep(1))
    {
        emit logMessage("校准过程被用户中止");
        emit calibrationFinished(false);
        return;
    }

    // 存储每个通道的最后一轮阻值和偏差
    // QVector<QPair<double, double>> finalResults(m_deviceConfig.num_channels, {0.0, 0.0});
    const int max_attempts = 3;

    // 标记当前通道是否合格
    bool channel_passed = false;

    // 获取参考通道和校准通道
    int calChannel = m_deviceConfig.cal_to_1220[channel - 1]; // 当前校准通道（根据映射表）

    QVector<double> resistances;

    // 打开参考电阻所在通道和当前校准设备通道
    QByteArray openSwitchs = createModbusCommand(createModbusOpen1220Frame(m_deviceConfig.ref_index[groupIndex], calChannel).toHex());
    auto openResult = sendCommand(openSwitchs, "1220_ControlSwitch");
    if (!openResult.first)
    {
        emit logMessage(QString("错误: 打开通道失败：%1").arg(openResult.second));
        emit calibrationFinished(false);
        return;
    }

    emit logMessage(QString("等待转换开关设备稳定 (%1秒)...").arg(m_deviceConfig.switchDelay));

    // 使用可中断等待，提高中止响应性
    if (!interruptibleSleep(m_deviceConfig.switchDelay))
    {
        emit logMessage("校准过程被用户中止");
        emit calibrationFinished(false);
        return;
    }

    // emit logMessage(QString("等待设备稳定 (10秒)..."));
    // QThread::sleep(10);  //等待20s

    for (int attempt = 1; attempt <= max_attempts; ++attempt)
    {
        if (m_abortRequested)
        {
            emit logMessage("校准过程被用户中止");
            emit calibrationFinished(false);
            return;
        }

        auto [passed, result] = read_resistances(channel - 1, attempt);
        if (passed)
        {
            channel_passed = true;
            resistances = result;
            emit logMessage(QString("通道 %1 复校结束!").arg(channel));
            break; // 读取四轮阻值都成功
        }

        if (attempt == max_attempts && !channel_passed)
        {
            emit logMessage(QString("通道 %1 经过 %2 次读取 4 轮阻值仍然失败！").arg(channel).arg(max_attempts));
            // finalResults[ch] = result;
            emit calibrationFinished(false);

            return; // 立即停止整个校准流程 非break
        }
    }
    ChannelData chData;
    chData.channelNumber = channel; // 通道号
    chData.measuredResistances = resistances;
    chData.referenceValue = referenceValue;
    chData.measuredResistance = std::accumulate(resistances.begin(), resistances.end(), 0.0) / resistances.size();
    ;                                                                           // 从串口读取当前通道四轮后的均值
    chData.deviationFromReference = chData.measuredResistance - referenceValue; // 阻值偏差Ω

    // 检测是否为TM18系列设备类型（TM18ND或TM18RD）
    bool isTM18SeriesType = m_deviceConfig.name.contains("TM18ND") || m_deviceConfig.name.contains("TM18RD");

    if (isTM18SeriesType)
    {
        // TM18系列设备：不计算等效温度偏差，直接使用阻值偏差进行判断
        chData.equivalentTempDeviation = 0.0;                                                               // TM18系列不使用等效温度偏差
        chData.allowedDeviation = calculateAllowedDevForTM18ND(m_deviceConfig.name, chData.referenceValue); // 返回欧姆单位的允许偏差
        chData.calibrationResult = (qAbs(chData.deviationFromReference) <= chData.allowedDeviation);        // 直接比较阻值偏差与允许偏差（欧姆）
    }
    else
    {
        // 标准设备：使用等效温度偏差进行判断
        chData.equivalentTempDeviation = calculateTempDev(m_deviceConfig.name, chData.referenceValue, chData.deviationFromReference); // 等效温度偏差mK（型号、参考阻值、阻值偏差
        chData.allowedDeviation = calculateAllowedDev(m_deviceConfig.name, chData.referenceValue);                                    // 允许偏差mK（设备型号、参照阻值Ω
        chData.calibrationResult = (qAbs(chData.equivalentTempDeviation) <= chData.allowedDeviation);                                 // 结果
    }

    // 发送信号更新UI 传入参考阻值、通道号（1-n)、4轮阻值、均值、均值偏差、允许偏差、等效温度偏差(mK)、允许偏差结果
    emit updateChannelData(chData.referenceValue, chData.channelNumber, chData.measuredResistances, chData.measuredResistance,
                           chData.deviationFromReference, chData.allowedDeviation, chData.equivalentTempDeviation, chData.calibrationResult);

    // 等待UI更新结果

    // 执行事件循环，等待结果或超时 等待mainwindow::uiUpdateFinished
    bool updateSuccessful = waitForUiUpdate(2000); // 2000ms timeout
    if (!updateSuccessful)
    {
        emit logMessage(QString("错误: UI更新超时，通道 %1").arg(channel));
        emit calibrationFinished(false);
        return;
    }

    // QThread::msleep(200);

    //    // 校准完成后关闭所有通道
    //    closeResult = sendCommand(closeSwitchs, "1220_ControlSwitch");
    //    if (!closeResult.first) {
    //        emit logMessage(QString("错误: 关闭通道失败：%1").arg(closeResult.second));
    //        emit calibrationFinished(false);
    //        return;
    //    }

    emit calibrationFinished(true);
}

void VerificationWorker::startCalibration()
{
    // 检查命令处理函数是否已设置
    if (!m_calCommandHandler || !m_deviceCommandHandler)
    {
        emit logMessage("错误: 命令处理函数未设置");
        emit calibrationFinished(false);
        return;
    }

    emit calibrationStarted(); // 输出："校准进程已启动"

    emit logMessage(QString("开始 %1 校准过程...").arg(m_deviceConfig.name));
    m_abortRequested = false;

    // 存储每个通道的最后一轮阻值和偏差
    // QVector<QPair<double, double>> finalResults(m_deviceConfig.num_channels, {0.0, 0.0});
    const int max_attempts = 3;

    ProjectData project;
    project.deviceModel = m_deviceConfig.name;

    // 优化后的逻辑：外层为通道循环，内层为参考阻值循环
    // 调试：输出参考通道配置
    QString refIndexStr;
    for (int i = 0; i < m_deviceConfig.ref_index.size(); i++)
    {
        if (i > 0)
            refIndexStr += ", ";
        refIndexStr += QString::number(m_deviceConfig.ref_index[i]);
    }
    emit logMessage(QString("参考通道配置: [%1], 参考阻值数量: %2").arg(refIndexStr).arg(m_deviceConfig.ref_index.size()));

    // 关闭所有通道（初始化状态）
    QByteArray closeSwitchs = createModbusCommand("01 10 00 03 00 02 04 00 00 00 00");
    auto closeResult = sendCommand(closeSwitchs, "1220_ControlSwitch");
    if (!closeResult.first)
    {
        emit logMessage("错误: 初始化关闭所有通道失败：" + closeResult.second);
        emit calibrationFinished(false);
        return;
    }

    // 使用可中断等待，提高中止响应性
    if (!interruptibleSleep(1))
    {
        emit logMessage("校准过程被用户中止");
        emit calibrationFinished(false);
        return;
    }

    for (int ch = 0; ch < m_deviceConfig.num_channels; ++ch)
    {
        if (m_abortRequested)
        {
            emit logMessage("校准过程被用户中止");
            emit calibrationFinished(false);
            return;
        }
        emit logMessage(QString("开始校准通道 %1 (所有阻值)...").arg(ch + 1));

        // 获取校准通道
        int calChannel = m_deviceConfig.cal_to_1220[ch]; // 当前校准通道（根据映射表）

        // 遍历所有参考电阻值
        for (int round = 0; round < m_deviceConfig.ref_index.size(); round++)
        {
            if (m_abortRequested)
            {
                emit logMessage("校准过程被用户中止");
                emit calibrationFinished(false);
                return;
            }

            // 参考通道
            int referChannel = m_deviceConfig.ref_index[round];   // 用于打开通道
            double referValue = m_deviceConfig.ref_values[round]; // 当前参考阻值 计算偏差使用

            ReferenceData refData;
            refData.referenceName = QString("参考阻值%1").arg(referChannel); // 勾选的参考阻值索引
            refData.referenceValue = referValue;                             // 勾选的参考阻值对应值

            emit logMessage(QString("通道 %1 - 当前参考阻值为：%2").arg(ch + 1).arg(referValue));

            // 标记当前通道是否合格
            bool channel_passed = false;
            QVector<double> resistances;

            // 每个阻值都需要切换1220开关并等待稳定
            QByteArray openSwitchs = createModbusCommand(createModbusOpen1220Frame(referChannel, calChannel).toHex());
            auto openResult = sendCommand(openSwitchs, "1220_ControlSwitch");
            if (!openResult.first)
            {
                emit logMessage(QString("错误: 打开通道失败：%1").arg(openResult.second));
                emit calibrationFinished(false);
                return;
            }

            emit logMessage(QString("等待转换开关设备稳定 (%1秒)...").arg(m_deviceConfig.switchDelay));

            // 每次切换参考通道都需要等待完整的稳定时间
            if (!interruptibleSleep(m_deviceConfig.switchDelay))
            {
                emit logMessage("校准过程被用户中止");
                emit calibrationFinished(false);
                return;
            }

            for (int attempt = 1; attempt <= max_attempts; ++attempt)
            {
                if (m_abortRequested)
                {
                    emit logMessage("校准过程被用户中止");
                    emit calibrationFinished(false);
                    return;
                }

                auto [passed, result] = read_resistances(ch, attempt);
                if (passed)
                {
                    channel_passed = true;
                    resistances = result;
                    emit logMessage(QString("通道 %1 校准结束!").arg(ch + 1));
                    break; // 读取四轮阻值都成功
                }

                if (attempt == max_attempts && !channel_passed)
                {
                    emit logMessage(QString("通道 %1 经过 %2 次读取 4 轮阻值仍然失败！").arg(ch + 1).arg(max_attempts));
                    // finalResults[ch] = result;
                    emit calibrationFinished(false);

                    return; // 立即停止整个校准流程 非break
                }
            }
            ChannelData chData;
            chData.channelNumber = ch + 1; // 通道号
            chData.measuredResistances = resistances;
            chData.referenceValue = refData.referenceValue;
            chData.measuredResistance = std::accumulate(resistances.begin(), resistances.end(), 0.0) / resistances.size();
            ;                                                                                   // 从串口读取当前通道四轮后的均值
            chData.deviationFromReference = chData.measuredResistance - refData.referenceValue; // 阻值偏差Ω

            // 检测是否为TM18系列设备类型（TM18ND或TM18RD）
            bool isTM18SeriesType = project.deviceModel.contains("TM18ND") || project.deviceModel.contains("TM18RD");

            if (isTM18SeriesType)
            {
                // TM18系列设备：不计算等效温度偏差，直接使用阻值偏差进行判断
                chData.equivalentTempDeviation = 0.0;                                                               // TM18系列不使用等效温度偏差
                chData.allowedDeviation = calculateAllowedDevForTM18ND(project.deviceModel, chData.referenceValue); // 返回欧姆单位的允许偏差
                chData.calibrationResult = (qAbs(chData.deviationFromReference) <= chData.allowedDeviation);        // 直接比较阻值偏差与允许偏差（欧姆）
            }
            else
            {
                // 标准设备：使用等效温度偏差进行判断
                chData.equivalentTempDeviation = calculateTempDev(project.deviceModel, chData.referenceValue, chData.deviationFromReference); // 等效温度偏差mK（型号、参考阻值、阻值偏差
                chData.allowedDeviation = calculateAllowedDev(project.deviceModel, chData.referenceValue);                                    // 允许偏差mK（设备型号、参照阻值Ω
                chData.calibrationResult = (qAbs(chData.equivalentTempDeviation) <= chData.allowedDeviation);                                 // 结果
            }

            // 发送信号更新UI 传入参考阻值、通道号（1-n)、4轮阻值、均值、均值偏差、允许偏差、等效温度偏差(mK)、允许偏差结果
            emit updateChannelData(chData.referenceValue, chData.channelNumber, chData.measuredResistances, chData.measuredResistance,
                                   chData.deviationFromReference, chData.allowedDeviation, chData.equivalentTempDeviation, chData.calibrationResult);

            // 等待UI更新结果
            // 执行事件循环，等待结果或超时 等待mainwindow::uiUpdateFinished
            bool updateSuccessful = waitForUiUpdate(2000); // 2000ms timeout
            if (!updateSuccessful)
            {
                emit logMessage(QString("错误: UI更新超时，通道 %1").arg(ch + 1));
                emit calibrationFinished(false);
                return;
            }

            // 在每个阻值完成后更新进度
            if (m_isBatchMode)
            {
                // 批量模式：发送已完成的步骤数，让BatchVerificationManager计算全局进度
                int currentBatchStep = ch * m_deviceConfig.ref_index.size() + round + 1;
                qDebug() << "VerificationWorker进度计算(批量模式): 通道" << ch << "阻值轮次" << round
                         << "已完成步骤" << currentBatchStep;
                emit calibrationProgress(ch, currentBatchStep);
            }
            else
            {
                // 单独模式：直接计算并发送进度百分比
                int totalSteps = m_deviceConfig.num_channels * m_deviceConfig.ref_index.size();
                int currentStep = ch * m_deviceConfig.ref_index.size() + round + 1;
                int progressPercent = (currentStep * 100) / totalSteps;
                qDebug() << "VerificationWorker进度计算(单独模式): 通道" << ch << "阻值轮次" << round
                         << "进度" << progressPercent << "%";
                emit calibrationProgress(ch, progressPercent);
            }

            emit logMessage(QString("通道 %1 阻值 %2 校准完成!").arg(ch + 1).arg(referValue));

            // 将当前阻值的数据添加到对应的参考数据中
            // 查找或创建对应的ReferenceData
            ReferenceData *targetRefData = nullptr;
            for (auto &ref : project.referenceValues)
            {
                if (ref.referenceValue == referValue)
                {
                    targetRefData = &ref;
                    break;
                }
            }

            if (!targetRefData)
            {
                // 如果不存在，创建新的ReferenceData
                ReferenceData newRefData;
                newRefData.referenceName = QString("参考阻值%1").arg(referChannel);
                newRefData.referenceValue = referValue;
                project.referenceValues.append(newRefData);
                targetRefData = &project.referenceValues.last();
            }

            targetRefData->channels.append(chData);
        } // 结束阻值循环
    } // 结束通道循环

    emit logMessage("所有通道校准完成!");

    // 只在非批量模式下发送100%进度
    if (!m_isBatchMode)
    {
        emit calibrationProgress(m_deviceConfig.num_channels - 1, 100);
    }

    emit calibrationFinished(true);

    // 告知主线程调用 saveProjectToDatabase 保存校准结果到数据库中
    emit saveResultsRequested();
}

void VerificationWorker::abortVerification()
{
    m_abortRequested = true;
    emit logMessage("正在中止校准过程...");

    // 请求当前线程中断，提高响应性
    if (QThread::currentThread())
    {
        QThread::currentThread()->requestInterruption();
    }
}

bool VerificationWorker::waitForUiUpdate(int timeoutMs)
{
    // 创建一个事件循环
    QEventLoop loop;
    bool completed = false;

    // 创建一个定时器，用于超时
    QTimer timer;
    timer.setSingleShot(true);
    timer.setInterval(timeoutMs);

    // 当收到 UI 更新完成的信号时，设置 completed 标志并退出事件循环
    auto connection = connect(this, &VerificationWorker::uiUpdateCompletedSignal,
                              [&loop, &completed]()
                              {
                                  completed = true;
                                  loop.quit();
                              });

    // 当定时器超时时，退出事件循环（completed 仍为 false）
    connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);

    // 启动定时器
    timer.start();

    // 运行事件循环，直到收到信号或超时
    loop.exec();

    // 断开连接以避免内存泄漏
    disconnect(connection);

    return completed;
}

void VerificationWorker::uiUpdateCompleted()
{
    // emit logMessage(QString("接收到UI更新结果"));
    emit uiUpdateCompletedSignal(); // 新增一个信号用于通知等待函数
}

bool VerificationWorker::is_channel_passed(const QVector<double> &deviations)
{
    if (deviations.size() < 3)
        return false;
    for (int i = deviations.size() - 3; i < deviations.size(); ++i)
    {
        if (deviations[i] >= 400)
            return false; // 测试偏差
    }
    return true;
}

double VerificationWorker::read_resistance(int channel)
{
    // 获取当前设备的精度类型
    FloatPrecision precision = getDevicePrecision();
    QByteArray frame = createModbusReadFrame(0x01, m_deviceConfig.read_addr, channel, 1, precision); // 头地址、读取首地址、需要读取第几个通道、读取的总通道数

    QPair<bool, QString> result = sendCommand(
        createModbusCommand(QString::fromLatin1(frame.toHex())),
        "Cal_ReadARoundRVals");

    double resistances;
    if (result.first)
    {
        double resistance = result.second.toDouble();
        resistances = resistance;
    }
    else
    {
        emit logMessage(QString("读取通道 %1 数据失败: %2").arg(channel + 1).arg(result.second));
        resistances = 0.0; // 错误时返回默认值
    }

    return resistances; // 读取单通道值
}

QPair<bool, QVector<double>> VerificationWorker::read_resistances(int channel, int attempt)
{
    emit logMessage(QString("通道 %1 第 %2 次读取 4 轮阻值开始...").arg(channel + 1).arg(attempt));

    // 写入参考阻值
    /*QByteArray frame = createModbusWriteFrame(0x01, m_deviceConfig.write_addr, channel, m_deviceConfig.ref_values[channel]);
    auto writeResult = sendCommand(createModbusCommand(QString::fromLatin1(frame.toHex())), "Cal_WriteCalParams");
    if (!writeResult.first) {
        emit logMessage(QString("通道 %1 写入参考阻值失败：%2").arg(channel+1).arg(writeResult.second));
        return QPair<bool, QVector<double>>(false, QVector<double>(4, 0.0));
    }
    // 等待10秒
    emit logMessage(QString("等待设备稳定 (10秒)..."));
    for (int i = 0; i < 10; ++i) {
        if (m_abortRequested) return QPair<bool, QVector<double>>(false, QVector<double>(4, 0.0));
        QThread::sleep(1);
    }*/

    // 读取1轮阻值数据
    QVector<double> resistances;

    double lastResistance = 0.0;
    for (int round = 0; round < 4; ++round)
    {
        if (m_abortRequested)
            return QPair<bool, QVector<double>>(false, QVector<double>(4, 0.0));

        double resistance = read_resistance(channel); // channel = 0 -> CH1
        if (resistance == 0.0)
        {
            // emit logMessage(QString("通道 %1 读取阻值失败，轮次：%2").arg(channel+1).arg(round+1));
            return QPair<bool, QVector<double>>(false, QVector<double>(4, 0.0));
        }

        lastResistance = resistance;

        qDebug() << "lastResistance: " << lastResistance;
        resistances.append(lastResistance);

        // 使用可中断等待，提高中止响应性
        if (!interruptibleMSleep(2000))
        {
            return QPair<bool, QVector<double>>(false, QVector<double>(4, 0.0));
        }
    }

    return QPair<bool, QVector<double>>(true, resistances);
}

// 允许偏差
double VerificationWorker::calculateAllowedDev(const QString &deviceModel, double refResValue)
{
    // 使用静态QMap，只会初始化一次
    static const QMap<QString, std::function<double(double)>> deviceRules = {
        {"618A RTD", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 100.0) < 50.0)
             {
                 return 150.0; // 接近 10.0
             }
             else if (qAbs(refResValue - 200.0) < 100.0)
             {
                 return 270.0; // 接近 100.0
             }
             else if (qAbs(refResValue - 350.0) < 175.0)
             {
                 return 510.0; // 接近 350.0
             }
             return 0.0; // 默认值
         }},

        {"618A RTD PLUS", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 100.0) < 50.0)
             {
                 return 100.0; // 接近 10.0
             }
             else if (qAbs(refResValue - 200.0) < 100.0)
             {
                 return 180.0; // 接近 100.0
             }
             else if (qAbs(refResValue - 350.0) < 175.0)
             {
                 return 340.0; // 接近 350.0
             }
             return 0.0; // 默认值
         }},

        {"619A RTD PLUS", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 100.0) < 50.0)
             {
                 return 50.0; // 接近 10.0
             }
             else if (qAbs(refResValue - 200.0) < 100.0)
             {
                 return 90.0; // 接近 100.0
             }
             else if (qAbs(refResValue - 350.0) < 175.0)
             {
                 return 170.0; // 接近 350.0
             }
             return 0.0; // 默认值
         }},

        {"618A PLUS", [](double) -> double
         { return 5.0; }},
        {"618A PLUS(6NTC+2P-AP23)", [](double) -> double
         { return 5.0; }},
        {"618A", [](double) -> double
         { return 10.0; }},

        {"619A PLUS", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 1000.0) < 500.0)
             {
                 return 10.0; // 接近 1k
             }
             else if (qAbs(refResValue - 10000.0) < 5000.0)
             {
                 return 1.0; // 接近 10k
             }
             else if (qAbs(refResValue - 20000.0) < 10000.0)
             {
                 return 1.0; // 接近 20k
             }
             return 0.0; // 默认值
         }},

        {"H-LCW-22B", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 1000.0) < 500.0)
             {
                 return 10.0; // 接近 1k
             }
             else if (qAbs(refResValue - 10000.0) < 5000.0)
             {
                 return 1.0; // 接近 10k
             }
             else if (qAbs(refResValue - 20000.0) < 10000.0)
             {
                 return 1.0; // 接近 20k
             }
             return 0.0; // 默认值
         }},

        {"TM14RD-PT100", [](double) -> double
         { return 50.0; }},
        {"TM14RD-PT1000", [](double) -> double
         { return 50.0; }},
        {"TM14ND", [](double) -> double
         { return 10.0; }},
        {"TM14ND-T", [](double) -> double
         { return 10.0; }},
        {"TM14ND-P", [](double) -> double
         { return 10.0; }},
        {"TM14ND-P-S", [](double) -> double
         { return 10.0; }},
        {"TM24ND-P-S", [](double) -> double
         { return 5.0; }},
        {"618A NTC-32", [](double) -> double
         { return 10.0; }},
        {"618A NTC-32-TIME", [](double) -> double
         { return 10.0; }},

        {"1611A-HT(PT100)", [](double) -> double
         { return 50.0; }},
        {"1611A-HT(PT1000)", [](double) -> double
         { return 50.0; }},

        {"619A NTC-32 PLUS", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 1000.0) < 500.0)
             {
                 return 1.0; // 接近 1k
             }
             else if (qAbs(refResValue - 10000.0) < 5000.0)
             {
                 return 1.0; // 接近 10k
             }
             else if (qAbs(refResValue - 20000.0) < 10000.0)
             {
                 return 1.0; // 接近 20k
             }
             return 0.0; // 默认值
         }},
        {"619A RTD-32 PLUS", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 100.0) < 50.0)
             {
                 return 15.0; // 接近 10.0
             }
             else if (qAbs(refResValue - 200.0) < 100.0)
             {
                 return 10.0; // 接近 100.0
             }
             else if (qAbs(refResValue - 350.0) < 175.0)
             {
                 return 30.0; // 接近 350.0
             }
             return 0.0; // 默认值
         }},
        // {"1618A", [](double refResValue) -> double {
        //      if (qAbs(refResValue - 1000.0) < 500.0) {
        //          return 1.0; // 接近 1k
        //      } else if (qAbs(refResValue - 10000.0) < 5000.0) {
        //          return 5.0; // 接近 10k
        //      } else if (qAbs(refResValue - 20000.0) < 10000.0) {
        //          return 10.0; // 接近 20k
        //      }
        //      return 0.0; // 默认值
        //  }},
    };

    // 查找设备规则，如果找不到则使用默认规则
    auto it = deviceRules.find(deviceModel);
    if (it != deviceRules.end())
    {
        // 使用找到的规则计算允许的偏差
        return it.value()(refResValue);
    }

    // 如果没有完全匹配，尝试处理类似 "1618A-N-NTC-03" 或 "1618A-L-NTC-03" 这种格式
    const QString prefix = "1618A-";
    if (deviceModel.startsWith(prefix))
    {
        QString suffix = deviceModel.mid(prefix.length()); // "N-NTC-03" 或 "L-NTC-03"
        QStringList parts = suffix.split('-');
        if (parts.size() == 3) // 新格式：1618A-N-NTC-03 或 1618A-L-NTC-03
        {
            const QString &subType = parts[0]; // "N" 或 "L"
            const QString &type = parts[1];    // "NTC"
            const QString &code = parts[2];    // "01", "02", "03"

            // 定义对应的映射规则（与旧格式相同）
            static const QMap<QString, QMap<QString, std::function<double(double)>>> thresholdMap1618A = {
                {"NTC", {{"01", [](double refResValue) -> double // 01->N19
                          {
                              // 1618A-01根据阻值细分：1000、10000、20000分别对应5、1、1
                              if (qAbs(refResValue - 1000.0) < 500.0)
                              {
                                  return 5.0; // 1000Ω对应5
                              }
                              else if (qAbs(refResValue - 10000.0) < 5000.0)
                              {
                                  return 1.0; // 10000Ω对应1
                              }
                              else if (qAbs(refResValue - 20000.0) < 10000.0)
                              {
                                  return 1.0; // 20000Ω对应1
                              }
                              return 1.0; // 默认值
                          }},
                         {"02", [](double) -> double
                          { return 5.0; }}, // 02 好像未使用
                         {"03", [](double) -> double
                          { return 10.0; }}, // 03->N18
                         {"default", [](double) -> double
                          { return 0.0; }}}},
                {"RTD", {{"01", [](double refResValue) -> double // 01-R19 高精度
                          {
                              // 1618A-R19根据阻值细分：1000、2000、100、200、350分别对应10、20、10、20、30
                              if (qAbs(refResValue - 1000.0) < 500.0)
                              {
                                  return 10.0; // 1000Ω对应10mK
                              }
                              else if (qAbs(refResValue - 2000.0) < 1000.0)
                              {
                                  return 20.0; // 2000Ω对应20mK
                              }
                              else if (qAbs(refResValue - 100.0) < 50.0)
                              {
                                  return 10.0; // 100Ω对应10mK
                              }
                              else if (qAbs(refResValue - 200.0) < 100.0)
                              {
                                  return 20.0; // 200Ω对应20mK
                              }
                              else if (qAbs(refResValue - 350.0) < 175.0)
                              {
                                  return 30.0; // 350Ω对应30mK
                              }
                              return 10.0; // 默认值
                          }},
                         {"02", [](double refResValue) -> double // 02-R18 低精度
                          {
                              // 1618A-R18根据阻值细分：1000、2000、100、200、350分别对应30、40、30、40、80
                              if (qAbs(refResValue - 1000.0) < 500.0)
                              {
                                  return 30.0; // 1000Ω对应30mK
                              }
                              else if (qAbs(refResValue - 2000.0) < 1000.0)
                              {
                                  return 40.0; // 2000Ω对应40mK
                              }
                              else if (qAbs(refResValue - 100.0) < 50.0)
                              {
                                  return 30.0; // 100Ω对应30mK
                              }
                              else if (qAbs(refResValue - 200.0) < 100.0)
                              {
                                  return 40.0; // 200Ω对应40mK
                              }
                              else if (qAbs(refResValue - 350.0) < 175.0)
                              {
                                  return 80.0; // 350Ω对应80mK
                              }
                              return 30.0; // 默认值
                          }},
                         {"03", [](double) -> double
                          { return 0.0; }},
                         {"default", [](double) -> double
                          { return 0.0; }}}},
                {"TC", {{"01", [](double) -> double
                         { return 0.0; }},
                        {"02", [](double) -> double
                         { return 0.0; }},
                        {"03", [](double) -> double
                         { return 0.0; }},
                        {"default", [](double) -> double
                         { return 0.0; }}}}};

            auto typeIt = thresholdMap1618A.find(type);
            if (typeIt != thresholdMap1618A.end())
            {
                const auto &codeMap = typeIt.value();
                if (codeMap.contains(code))
                {
                    return codeMap.value(code)(refResValue);
                }
                else if (codeMap.contains("default"))
                {
                    return codeMap.value("default")(refResValue);
                }
            }
        }
        else if (parts.size() == 2) // 兼容旧格式：1618A-NTC-03
        {
            const QString &type = parts[0]; // "NTC"
            const QString &code = parts[1]; // "03"->18  "01"->19

            // 定义对应的映射规则
            static const QMap<QString, QMap<QString, std::function<double(double)>>> thresholdMap1618A = {
                {"NTC", {{"01", [](double refResValue) -> double
                          {
                              // 1618A-01根据阻值细分：1000、10000、20000分别对应5、1、1
                              if (qAbs(refResValue - 1000.0) < 500.0)
                              {
                                  return 5.0; // 1000Ω对应5
                              }
                              else if (qAbs(refResValue - 10000.0) < 5000.0)
                              {
                                  return 1.0; // 10000Ω对应1
                              }
                              else if (qAbs(refResValue - 20000.0) < 10000.0)
                              {
                                  return 1.0; // 20000Ω对应1
                              }
                              return 1.0; // 默认值
                          }},
                         {"02", [](double) -> double
                          { return 5.0; }},
                         {"03", [](double) -> double
                          { return 10.0; }},
                         {"default", [](double) -> double
                          { return 0.0; }}}},
                {"RTD", {{"01", [](double) -> double
                          { return 0.0; }},
                         {"02", [](double) -> double
                          { return 0.0; }},
                         {"03", [](double) -> double
                          { return 0.0; }},
                         {"default", [](double) -> double
                          { return 0.0; }}}},
                {"TC", {{"01", [](double) -> double
                         { return 0.0; }},
                        {"02", [](double) -> double
                         { return 0.0; }},
                        {"03", [](double) -> double
                         { return 0.0; }},
                        {"default", [](double) -> double
                         { return 0.0; }}}}};

            auto typeIt = thresholdMap1618A.find(type);
            if (typeIt != thresholdMap1618A.end())
            {
                const auto &codeMap = typeIt.value();
                if (codeMap.contains(code))
                {
                    return codeMap.value(code)(refResValue);
                }
                else if (codeMap.contains("default"))
                {
                    return codeMap.value("default")(refResValue);
                }
            }
        }
        // 格式不符合或未匹配，返回默认
        return 0.0;
    }

    // 默认规则
    return 10.0;
}

// 计算等效温度偏差
double VerificationWorker::calculateTempDev(const QString &deviceModel, double refResValue, double devResValue) // 设备型号、参考阻值、阻值偏差(测量值与参考阻值的差值)
{
    double S = calculateS(refResValue, false, deviceModel); // 当前只允许Ω

    qDebug() << "S:" << S;

    double tempDev = S != 0 ? devResValue / S : 0.0;

    double adjustedTempDev = tempDev * 10.0 + (tempDev > 0 ? 0.0000001 : -0.0000001);

    double roundedTempDev = std::round(adjustedTempDev) / 10.0;
    return roundedTempDev;
    // double roundedTempDev = std::round(adjustedTempDev) / 10.0;
    // return QString::number(roundedTempDev, 'f', 1);
}

// 获取S值
double VerificationWorker::calculateS(double refResValue, bool isKOhm, const QString &deviceModel)
{
    // 统一参考值（全部转为欧姆）
    double refOhms = isKOhm ? refResValue * 1000.0 : refResValue;

    // 新增型号直接返回固定系数
    if (deviceModel == "1611A-HT(PT100)")
    {
        return 0.000385;
    }

    if (deviceModel == "1611A-HT(PT1000)")
    {
        return 0.00385;
    }

    // 检查1618A系列的RTD板卡（包括1618A-N和1618A-L）
    if (deviceModel.startsWith("1618A-N-RTD-") || deviceModel.startsWith("1618A-L-RTD-"))
    {
        // 1618A RTD板卡使用固定的RTD系数
        if (qAbs(refOhms - 100.0) < 50.0 || qAbs(refOhms - 200.0) < 100.0 || qAbs(refOhms - 350.0) < 175.0)
        {
            return 0.000385; // 100Ω、200Ω、350Ω使用PT100系数
        }
        else if (qAbs(refOhms - 1000.0) < 500.0 || qAbs(refOhms - 2000.0) < 1000.0)
        {
            return 0.00385; // 1000Ω、2000Ω使用PT1000系数
        }
    }

    // 误差范围设置
    double epsilon = 10.0; // 默认误差范围为10欧姆

    // 设备类型到系数映射的映射
    using CoefficientMap = QMap<double, double>;
    static const QMap<QString, CoefficientMap> deviceCoefficients = {
        // RTD设备
        {"618A RTD", {{100.0, 0.000385}, {200.0, 0.000385}, {350.0, 0.000385}, {1000.0, 0.00385}, {2000.0, 0.00385}}},
        {"618A RTD PLUS", {{100.0, 0.000385}, {200.0, 0.000385}, {350.0, 0.000385}, {1000.0, 0.00385}, {2000.0, 0.00385}}},
        {"619A RTD PLUS", {{100.0, 0.000385}, {200.0, 0.000385}, {350.0, 0.000385}, {1000.0, 0.00385}, {2000.0, 0.00385}}},
        {"TM14RD-PT100", {{100.0, 0.000385}, {200.0, 0.000385}, {350.0, 0.000385}, {1000.0, 0.000385}, {2000.0, 0.000385}}},
        {"TM14RD-PT1000", {{100.0, 0.00385}, {200.0, 0.00385}, {350.0, 0.00385}, {1000.0, 0.00385}, {2000.0, 0.00385}}},

        // 默认设备配置（其他所有设备）
        {"DEFAULT", {{100.0, 0.00772}, {200.0, 0.011552}, {1000.0, 0.026}, {5000.0, 0.17}, {10000.0, 0.4}, {20000.0, 0.9}}}};

    // 确定要使用的系数映射
    const CoefficientMap &coefficients = deviceCoefficients.contains(deviceModel)
                                             ? deviceCoefficients[deviceModel]
                                             : deviceCoefficients["DEFAULT"];

    // 查找最接近的参考电阻值
    for (auto it = coefficients.begin(); it != coefficients.end(); ++it)
    {
        if (std::abs(refOhms - it.key()) < epsilon)
        {
            return it.value();
        }
    }

    return 0.0; // 单位 -- Ω/mK
}

// 添加一个新的辅助方法，用于确定设备需要的浮点精度类型
FloatPrecision VerificationWorker::getDevicePrecision() const
{
    // 使用单精度浮点数的设备列表
    static const QSet<QString> singlePrecisionDevices = {
        "618A NTC-32-TIME",
        "1611A-HT(PT100)",
        "1611A-HT(PT1000)",
    };

    // 如果设备在单精度列表中，返回单精度，否则返回双精度
    return singlePrecisionDevices.contains(m_deviceConfig.name) ? FloatPrecision::Single : FloatPrecision::Double;
}

QByteArray VerificationWorker::createModbusReadFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, int numChannels, FloatPrecision precision)
{
    // 计算每个通道占用的寄存器数量
    int registersPerChannel = (precision == FloatPrecision::Single) ? 2 : 4;

    // 计算起始寄存器地址
    uint16_t addr = firstAddr + channel * registersPerChannel;
    uint8_t addr_high = (addr >> 8) & 0xFF; // 提取高字节
    uint8_t addr_low = addr & 0xFF;         // 提取低字节

    // 计算需要读取的寄存器数量
    uint16_t numRegisters = numChannels * registersPerChannel;
    uint8_t numRegisters_high = (numRegisters >> 8) & 0xFF; // 提取高字节
    uint8_t numRegisters_low = numRegisters & 0xFF;         // 提取低字节

    // 构造 Modbus RTU 请求帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr));        // 设备地址
    frame.append(static_cast<char>(0x03));              // 功能码 03（读取保持寄存器）
    frame.append(static_cast<char>(addr_high));         // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));          // 寄存器地址低字节
    frame.append(static_cast<char>(numRegisters_high)); // 寄存器数量高字节
    frame.append(static_cast<char>(numRegisters_low));  // 寄存器数量低字节

    return frame;
}

QByteArray VerificationWorker::createModbusWriteFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, double ref_value, FloatPrecision precision)
{
    // 计算每个通道占用的寄存器数量
    int registersPerChannel = (precision == FloatPrecision::Single) ? 2 : 4;

    // 计算该通道的寄存器地址
    uint16_t addr = firstAddr + channel * registersPerChannel;
    uint8_t addr_high = (addr >> 8) & 0xFF; // 高字节
    uint8_t addr_low = addr & 0xFF;         // 低字节

    // 准备数据 - 多个寄存器写入统一使用小端模式
    QByteArray byteArray;
    QDataStream stream(&byteArray, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);

    if (precision == FloatPrecision::Single)
    {
        // 对于单精度，将 double 转换为 float，并确保只写入 4 个字节
        float float_value = static_cast<float>(ref_value);
        byteArray.resize(4);                       // 4 个字节
        memcpy(byteArray.data(), &float_value, 4); // 写入数据
    }
    else
    {
        // 对于双精度，直接写入 double，确保写入 8 个字节
        byteArray.resize(8);                     // 8 个字节
        memcpy(byteArray.data(), &ref_value, 8); // 写入数据
    }
    // 计算字节数
    int byteCount = (precision == FloatPrecision::Single) ? 4 : 8;

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr));          // 设备地址
    frame.append(static_cast<char>(0x10));                // 功能码 10（写多个寄存器）
    frame.append(static_cast<char>(addr_high));           // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));            // 寄存器地址低字节
    frame.append(static_cast<char>(0x00));                // 寄存器数量高字节
    frame.append(static_cast<char>(registersPerChannel)); // 寄存器数量低字节
    frame.append(static_cast<char>(byteCount));           // 字节数
    frame.append(byteArray);                              // 数据

    return frame;
}

QByteArray VerificationWorker::createModbusOpen1220Frame(int referChannel, int calChannel)
{
    // 创建一个4字节的数组，初始化为0
    QByteArray byteArray(4, 0);

    // 设置referChannel对应的位
    if (referChannel >= 1 && referChannel <= 20)
    {
        int byteIndex = (referChannel - 1) / 8;   // 确定在哪个字节
        int bitPosition = (referChannel - 1) % 8; // 确定在字节中的位置
        char byte = byteArray[byteIndex];         // 获取当前字节
        byte |= (0x01 << bitPosition);            // 设置位
        byteArray[byteIndex] = byte;              // 将修改后的字节写回数组
    }

    // 设置calChannel对应的位
    if (calChannel >= 1 && calChannel <= 20)
    {
        int byteIndex = (calChannel - 1) / 8;
        int bitPosition = (calChannel - 1) % 8;
        char byte = byteArray[byteIndex];
        byte |= (0x01 << bitPosition);
        byteArray[byteIndex] = byte;
    }

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(0x01)); // 设备地址
    frame.append(static_cast<char>(0x10)); // 功能码 10
    frame.append(static_cast<char>(0x00)); // 寄存器地址高字节
    frame.append(static_cast<char>(0x03)); // 寄存器地址低字节
    frame.append(static_cast<char>(0x00)); // 寄存器数量高字节
    frame.append(static_cast<char>(0x02)); // 寄存器数量低字节
    frame.append(static_cast<char>(0x04)); // 字节数
    frame.append(byteArray);               // 添加生成的4字节数据

    return frame;
}

QByteArray VerificationWorker::createModbusCommand(const QString &hexCommand)
{
    // 创建基础RTU数据（不含CRC）
    QByteArray rtuData = createModbusRtuFrame(hexCommand);

    // 根据设备型号选择协议格式
    if (isTcpDevice(m_deviceConfig.name))
    {
        // TCP设备：创建TCP帧格式
        return createModbusTcpFrame(rtuData);
    }
    else
    {
        // RTU设备：添加CRC校验
        uint16_t crc = calculateCRC16(rtuData);
        rtuData.append(static_cast<char>(crc & 0xFF));
        rtuData.append(static_cast<char>((crc >> 8) & 0xFF));
        return rtuData;
    }
}

uint16_t VerificationWorker::calculateCRC16(const QByteArray &data)
{
    uint16_t crc = 0xFFFF;
    for (char ch : data)
    {
        crc ^= static_cast<uint8_t>(ch);
        for (int i = 0; i < 8; ++i)
        {
            if (crc & 0x0001)
            {
                crc = (crc >> 1) ^ 0xA001;
            }
            else
            {
                crc = crc >> 1;
            }
        }
    }
    return crc;
}

// TCP协议支持函数实现
bool VerificationWorker::isTcpDevice(const QString &deviceModel)
{
    // 定义使用TCP协议的设备型号列表
    QStringList tcpDevices = {"TM14ND-P-LAN"};
    return tcpDevices.contains(deviceModel);
}

QByteArray VerificationWorker::createModbusTcpFrame(const QByteArray &rtuData)
{
    // TCP帧结构：事务处理标识(2字节) + 协议标识(2字节) + 长度(2字节) + 单元标识符 + 功能码 + 数据
    QByteArray tcpFrame;

    // 事务处理标识 (2字节) - 使用固定值0x0000
    tcpFrame.append(static_cast<char>(0x00));
    tcpFrame.append(static_cast<char>(0x00));

    // 协议标识 (2字节) - Modbus协议固定为0x0000
    tcpFrame.append(static_cast<char>(0x00));
    tcpFrame.append(static_cast<char>(0x00));

    // 长度字段 (2字节) - 后续字节数（不包括事务处理标识和协议标识）
    uint16_t length = rtuData.size();
    tcpFrame.append(static_cast<char>((length >> 8) & 0xFF)); // 长度高字节
    tcpFrame.append(static_cast<char>(length & 0xFF));        // 长度低字节

    // 添加RTU数据（不包括CRC）
    tcpFrame.append(rtuData);

    return tcpFrame;
}

QByteArray VerificationWorker::createModbusRtuFrame(const QString &hexCommand)
{
    // 移除所有空格
    QString cleanCommand = hexCommand.simplified().remove(' ');

    // 将十六进制字符串转换为 QByteArray
    QByteArray cmd = QByteArray::fromHex(cleanCommand.toLatin1());

    return cmd; // 返回不带CRC的RTU数据
}

QPair<bool, QString> VerificationWorker::sendCommand(const QByteArray &command, const QString &commandType)
{
    // 根据命令类型选择合适的命令处理函数
    if (commandType.startsWith("Cal_"))
    {
        if (m_calCommandHandler)
        {
            return m_calCommandHandler(command, commandType);
        }
    }
    else if (commandType.startsWith("1220_"))
    {
        if (m_deviceCommandHandler)
        {
            return m_deviceCommandHandler(command, commandType);
        }
    }

    return {false, "未知的命令类型或命令处理函数未设置"};
}

// TM18系列设备专用允许偏差计算（返回欧姆单位）
double VerificationWorker::calculateAllowedDevForTM18ND(const QString &deviceModel, double refResValue)
{
    // TM18系列设备的允许偏差映射（欧姆单位）
    static const QMap<QString, std::function<double(double)>> tm18DeviceRules = {
        {"TM18ND-P", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 1000.0) < 500.0)
             {
                 return 0.2;
             }
             else if (qAbs(refResValue - 10000.0) < 5000.0)
             {
                 return 0.3;
             }
             else if (qAbs(refResValue - 20000.0) < 10000.0)
             {
                 return 1.6;
             }
             return 0.0; // 默认值
         }},
        {"TM18RD-P", [](double refResValue) -> double
         {
             if (qAbs(refResValue - 350.0) < 175.0)
             {
                 return 0.04;
             }
             else if (qAbs(refResValue - 1000.0) < 500.0)
             {
                 return 0.05;
             }
             else if (qAbs(refResValue - 2000.0) < 1000.0)
             {
                 return 0.1;
             }
             return 0.0; // 默认值
         }},
    };

    // 查找设备规则
    auto it = tm18DeviceRules.find(deviceModel);
    if (it != tm18DeviceRules.end())
    {
        return it.value()(refResValue);
    }

    // 如果没有找到精确匹配，检查是否包含TM18ND或TM18RD
    if (deviceModel.contains("TM18ND"))
    {
        // 使用默认的TM18ND规则
        if (qAbs(refResValue - 1000.0) < 500.0)
        {
            return 0.2;
        }
        else if (qAbs(refResValue - 10000.0) < 5000.0)
        {
            return 0.3;
        }
        else if (qAbs(refResValue - 20000.0) < 10000.0)
        {
            return 1.6;
        }
    }
    else if (deviceModel.contains("TM18RD"))
    {
        // 使用默认的TM18RD规则
        if (qAbs(refResValue - 350.0) < 175.0)
        {
            return 0.04;
        }
        else if (qAbs(refResValue - 1000.0) < 500.0)
        {
            return 0.05;
        }
        else if (qAbs(refResValue - 2000.0) < 1000.0)
        {
            return 0.1;
        }
    }

    // 默认值
    return 0.0;
}

bool VerificationWorker::interruptibleSleep(int seconds)
{
    for (int i = 0; i < seconds; ++i)
    {
        if (m_abortRequested || QThread::currentThread()->isInterruptionRequested())
        {
            return false; // 被中断
        }
        QThread::sleep(1);
    }
    return true; // 正常完成
}

bool VerificationWorker::interruptibleMSleep(int milliseconds)
{
    const int interval = 100; // 每100ms检查一次
    int remaining = milliseconds;

    while (remaining > 0)
    {
        if (m_abortRequested || QThread::currentThread()->isInterruptionRequested())
        {
            return false; // 被中断
        }

        int sleepTime = qMin(interval, remaining);
        QThread::msleep(sleepTime);
        remaining -= sleepTime;
    }
    return true; // 正常完成
}
