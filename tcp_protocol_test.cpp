#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <iomanip>
#include <algorithm>

// TCP协议测试类
class TcpProtocolTest
{
public:
    // 测试用的TCP协议支持函数
    static bool isTcpDevice(const std::string &deviceModel)
    {
        std::vector<std::string> tcpDevices = {"TM14ND-P-LAN"};
        return std::find(tcpDevices.begin(), tcpDevices.end(), deviceModel) != tcpDevices.end();
    }

    static std::vector<uint8_t> createModbusTcpFrame(const std::vector<uint8_t> &rtuData)
    {
        std::vector<uint8_t> tcpFrame;

        // 事务处理标识 (2字节) - 使用固定值0x0000
        tcpFrame.push_back(0x00);
        tcpFrame.push_back(0x00);

        // 协议标识 (2字节) - Modbus协议固定为0x0000
        tcpFrame.push_back(0x00);
        tcpFrame.push_back(0x00);

        // 长度字段 (2字节) - 后续字节数
        uint16_t length = rtuData.size();
        tcpFrame.push_back((length >> 8) & 0xFF);
        tcpFrame.push_back(length & 0xFF);

        // 添加RTU数据
        tcpFrame.insert(tcpFrame.end(), rtuData.begin(), rtuData.end());

        return tcpFrame;
    }

    static std::vector<uint8_t> createModbusRtuFrame(const std::string &hexCommand)
    {
        std::string cleanCommand = hexCommand;
        cleanCommand.erase(std::remove(cleanCommand.begin(), cleanCommand.end(), ' '), cleanCommand.end());

        std::vector<uint8_t> cmd;
        for (size_t i = 0; i < cleanCommand.length(); i += 2)
        {
            std::string byteString = cleanCommand.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
            cmd.push_back(byte);
        }
        return cmd;
    }

    static uint16_t calculateCRC16(const std::vector<uint8_t> &data)
    {
        uint16_t crc = 0xFFFF;
        for (uint8_t ch : data)
        {
            crc ^= ch;
            for (int i = 0; i < 8; ++i)
            {
                if (crc & 0x0001)
                {
                    crc = (crc >> 1) ^ 0xA001;
                }
                else
                {
                    crc = crc >> 1;
                }
            }
        }
        return crc;
    }

    static std::vector<uint8_t> createModbusCommand(const std::string &hexCommand, const std::string &deviceModel)
    {
        std::vector<uint8_t> rtuData = createModbusRtuFrame(hexCommand);

        if (isTcpDevice(deviceModel))
        {
            return createModbusTcpFrame(rtuData);
        }
        else
        {
            uint16_t crc = calculateCRC16(rtuData);
            rtuData.push_back(crc & 0xFF);
            rtuData.push_back((crc >> 8) & 0xFF);
            return rtuData;
        }
    }

    static std::string vectorToHexString(const std::vector<uint8_t> &data)
    {
        std::stringstream ss;
        for (size_t i = 0; i < data.size(); ++i)
        {
            if (i > 0)
                ss << " ";
            ss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(data[i]);
        }
        return ss.str();
    }

    // 测试函数
    static void runTests()
    {
        std::cout << "=== TCP协议转换测试 ===" << std::endl;

        // 测试1：RTU设备协议转换
        testRtuDevice();

        // 测试2：TCP设备协议转换
        testTcpDevice();

        // 测试3：协议转换示例验证
        testProtocolConversion();

        std::cout << "=== 测试完成 ===" << std::endl;
    }

private:
    static void testRtuDevice()
    {
        std::cout << "\n--- 测试RTU设备 ---" << std::endl;

        std::string deviceModel = "TM14ND-P";
        std::string hexCommand = "01 03 00 11 00 05";

        std::vector<uint8_t> result = createModbusCommand(hexCommand, deviceModel);

        std::cout << "设备型号: " << deviceModel << std::endl;
        std::cout << "输入命令: " << hexCommand << std::endl;
        std::cout << "输出结果: " << vectorToHexString(result) << std::endl;
        std::cout << "预期结果: 01 03 00 11 00 05 D5 CC (RTU格式+CRC)" << std::endl;

        // 验证结果
        std::vector<uint8_t> expected = {0x01, 0x03, 0x00, 0x11, 0x00, 0x05, 0xD5, 0xCC};
        bool passed = (result == expected);
        std::cout << "测试结果: " << (passed ? "通过" : "失败") << std::endl;
    }

    static void testTcpDevice()
    {
        std::cout << "\n--- 测试TCP设备 ---" << std::endl;

        std::string deviceModel = "TM14ND-P-LAN";
        std::string hexCommand = "01 03 00 11 00 05";

        std::vector<uint8_t> result = createModbusCommand(hexCommand, deviceModel);

        std::cout << "设备型号: " << deviceModel << std::endl;
        std::cout << "输入命令: " << hexCommand << std::endl;
        std::cout << "输出结果: " << vectorToHexString(result) << std::endl;
        std::cout << "预期结果: 00 00 00 00 00 06 01 03 00 11 00 05 (TCP格式)" << std::endl;

        // 验证结果
        std::vector<uint8_t> expected = {0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x01, 0x03, 0x00, 0x11, 0x00, 0x05};
        bool passed = (result == expected);
        std::cout << "测试结果: " << (passed ? "通过" : "失败") << std::endl;
    }

    static void testProtocolConversion()
    {
        qDebug() << "\n--- 测试协议转换示例 ---";

        // 测试用例：原始RTU命令转换为TCP格式
        struct TestCase
        {
            QString description;
            QString hexCommand;
            QString expectedTcp;
        };

        QList<TestCase> testCases = {
            {"读取寄存器", "01 03 00 11 00 05", "00 00 00 00 00 06 01 03 00 11 00 05"},
            {"写入单个寄存器", "01 06 00 0A 00 01", "00 00 00 00 00 06 01 06 00 0A 00 01"},
            {"读取波特率", "01 03 00 02 00 01", "00 00 00 00 00 06 01 03 00 02 00 01"}};

        QString deviceModel = "TM14ND-P-LAN";

        for (const auto &testCase : testCases)
        {
            qDebug() << "\n测试用例:" << testCase.description;

            QByteArray result = createModbusCommand(testCase.hexCommand, deviceModel);
            QByteArray expected = QByteArray::fromHex(testCase.expectedTcp.simplified().remove(' ').toLatin1());

            qDebug() << "输入:" << testCase.hexCommand;
            qDebug() << "输出:" << result.toHex(' ').toUpper();
            qDebug() << "预期:" << testCase.expectedTcp;

            bool passed = (result == expected);
            qDebug() << "结果:" << (passed ? "通过" : "失败");

            if (!passed)
            {
                qDebug() << "错误详情:";
                qDebug() << "  实际长度:" << result.size() << "字节";
                qDebug() << "  预期长度:" << expected.size() << "字节";
            }
        }
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    TcpProtocolTest::runTests();

    return 0;
}
