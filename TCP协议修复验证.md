# TCP协议修复验证

## 修复的问题

### 问题1：1220通道开关误用TCP协议
**问题描述**：1220通道开关命令被错误地转换为TCP格式
**原始错误**：`"00 00 00 00 00 0B 01 10 00 03 00 02 04 00 00 00 00"`
**正确格式**：`"01 10 00 03 00 02 04 00 00 00 00 XX XX"` (RTU格式+CRC)

**修复方案**：
1. 创建专门的`createModbus1220Command()`函数
2. 1220通道开关始终使用RTU协议，不受TCP设备影响
3. 修改所有Worker类中的1220命令调用

### 问题2：TCP协议返回数据处理缺失
**问题描述**：TCP协议返回的数据包含6字节TCP头部，需要去掉才能解析
**TCP返回格式**：`00 00 00 00 00 0D 01 03 0A FF FF FF FF FF FF FF FF FF FF`
**RTU数据部分**：`01 03 0A FF FF FF FF FF FF FF FF FF FF`

**修复方案**：
1. 在`calDeviceProcessor::processData()`中添加TCP头部检测
2. 自动去掉TCP头部，提取RTU数据部分
3. 确保所有数据处理使用处理后的数据

## 修复实现

### 1. 新增1220专用命令函数

#### mainwindow.h
```cpp
// 1220通道开关专用命令（始终使用RTU协议）
QByteArray createModbus1220Command(const QString &hexCommand);
```

#### mainwindow.cpp
```cpp
QByteArray MainWindow::createModbus1220Command(const QString &hexCommand)
{
    // 1220通道开关始终使用RTU协议，不受TCP设备影响
    QByteArray rtuData = createModbusRtuFrame(hexCommand);
    
    // 添加CRC校验
    uint16_t crc = calculateCRC16(rtuData);
    rtuData.append(static_cast<char>(crc & 0xFF));
    rtuData.append(static_cast<char>((crc >> 8) & 0xFF));
    
    return rtuData;
}
```

### 2. 修改Worker类中的1220命令调用

#### CalibrationWorker.cpp
```cpp
// 修改前
QByteArray openSwitchs = createModbusCommand(createModbusOpen1220Frame(referChannel, calChannel).toHex());

// 修改后
QByteArray openSwitchs = createModbus1220Command(createModbusOpen1220Frame(referChannel, calChannel).toHex());
```

#### VerificationWorker.cpp
```cpp
// 修改前
QByteArray closeSwitchs = createModbusCommand("01 10 00 03 00 02 04 00 00 00 00");

// 修改后
QByteArray closeSwitchs = createModbus1220Command("01 10 00 03 00 02 04 00 00 00 00");
```

### 3. 添加TCP返回数据处理

#### mainwindow.h - calDeviceProcessor
```cpp
void processData(const QByteArray &data, const QString &m_currentCommand) override
{
    QString command = m_currentCommand;
    
    // 处理TCP协议返回数据：去掉TCP头部
    QByteArray processedData = data;
    if (data.size() > 6 && data.at(0) == 0x00 && data.at(1) == 0x00 && 
        data.at(2) == 0x00 && data.at(3) == 0x00)
    {
        // TCP格式：去掉前6字节的TCP头部
        processedData = data.mid(6);
    }
    
    // 后续所有处理使用processedData而不是data
    if (command == "Cal_ZCLOG334NTC_EndCal")
    {
        if (processedData.size() >= 5)
        {
            QString hexData = processedData.toHex(' ');
            // ...
        }
    }
    // ...
}
```

## 验证测试

### 1. 1220通道开关命令验证
```cpp
// 测试用例
QString hexCommand = "01 10 00 03 00 02 04 00 00 00 00";

// RTU设备 (TM14ND-P)
QByteArray rtuResult = createModbus1220Command(hexCommand);
// 预期：01 10 00 03 00 02 04 00 00 00 00 XX XX (RTU+CRC)

// TCP设备 (TM14ND-P-LAN) - 1220命令仍使用RTU
QByteArray tcpResult = createModbus1220Command(hexCommand);
// 预期：01 10 00 03 00 02 04 00 00 00 00 XX XX (RTU+CRC，相同)
```

### 2. TCP返回数据处理验证
```cpp
// TCP返回数据示例
QByteArray tcpResponse = QByteArray::fromHex("0000000000070103044D2F4D2F");
// TCP头部：00 00 00 00 00 07
// RTU数据：01 03 04 4D 2F 4D 2F

// 处理后应该得到RTU数据部分
QByteArray processedData = tcpResponse.mid(6);
// 结果：01 03 04 4D 2F 4D 2F
```

## 预期效果

### 1. 1220通道开关
- ✅ 无论选择什么设备型号，1220通道开关始终使用RTU协议
- ✅ 命令格式正确：`01 10 00 03 00 02 04 00 00 00 00 + CRC`
- ✅ 不会出现TCP格式的1220命令

### 2. TCP设备通信
- ✅ TM14ND-P-LAN设备命令使用TCP格式
- ✅ TCP返回数据正确解析，去掉头部
- ✅ 数据处理逻辑正常工作

### 3. RTU设备兼容性
- ✅ 现有RTU设备功能不受影响
- ✅ 1220通道开关正常工作
- ✅ 所有数据处理保持一致

## 总结

通过这次修复：

1. **解决了1220通道开关协议混淆问题** - 创建专用函数确保始终使用RTU协议
2. **添加了TCP返回数据处理** - 自动检测并去掉TCP头部
3. **保持了系统兼容性** - RTU和TCP设备都能正常工作
4. **提高了代码清晰度** - 明确区分了设备通信和1220通信

现在系统可以正确处理：
- TM14ND-P-LAN设备的TCP协议通信
- 1220通道开关的RTU协议通信
- TCP协议的返回数据解析
- 所有现有设备的正常功能
