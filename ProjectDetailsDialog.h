﻿#ifndef PROJECTDETAILSDIALOG_H
#define PROJECTDETAILSDIALOG_H

#include <QDialog>
#include <QTableWidget>
#include <QLabel>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QGroupBox>
#include <QPushButton>
#include <QScrollArea>

#include "ProjectDataTypes.h"

class ProjectDetailsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ProjectDetailsDialog(QWidget *parent = nullptr);
    ~ProjectDetailsDialog();

    void displayProjectData(const ProjectData& projectData);

private:
    // Helper methods
    void createProjectInfoSection();
    void createEnvironmentSection();
    void createCalDevicesSection();
    void createMeasurementsSection();    void fillProjectInfo(const ProjectData& projectData);
    void fillEnvironmentInfo(const AmbientInfo& ambientInfo);
    void fillCalDevicesInfo(const QList<DeviceInfo>& devices);
    void fillMeasurementsData(const ProjectData& projectData);

    // UI elements
    QGroupBox *projectInfoGroup;
    QGridLayout *projectInfoLayout;

    QGroupBox *environmentGroup;
    QGridLayout *environmentLayout;

    QGroupBox *devicesGroup;
    QGridLayout *devicesLayout;  // Changed from QTableWidget *devicesTable

    QGroupBox *measurementsGroup;
    QTableWidget *measurementsTable;

    QVBoxLayout *mainLayout;
    QScrollArea *scrollArea;
    QPushButton *closeButton;
};

#endif // PROJECTDETAILSDIALOG_H
