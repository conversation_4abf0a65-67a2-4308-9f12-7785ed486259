﻿#include "ProjectDetailsDialog.h"
#include <QHeaderView>
#include <QPushButton>
#include <QHBoxLayout>
#include <QFrame>
#include <QDateTime>
#include <QDesktopWidget>
#include <QApplication>
#include <QScreen>
#include <QTimer>

ProjectDetailsDialog::ProjectDetailsDialog(QWidget *parent) : QDialog(parent)
{
    setWindowTitle("项目详情");

    // Set white background for the dialog
    setAutoFillBackground(true);
    QPalette pal = palette();
    pal.setColor(QPalette::Window, Qt::white);
    setPalette(pal);

    // Calculate 80% of screen size and enforce it
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int width = screenGeometry.width() * 0.8;
    int height = screenGeometry.height() * 0.8;

    // Set minimum and fixed sizes to ensure the dialog is actually 80% of screen size
    setMinimumSize(width, height);
    resize(width, height);

    // Create scroll area for content
    scrollArea = new QScrollArea(this);
    scrollArea->setWidgetResizable(true);
    scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea->setFrameShape(QFrame::NoFrame); // Remove frame around scroll area

    // Create a widget to hold all content with white background
    QWidget *contentWidget = new QWidget();
    contentWidget->setAutoFillBackground(true);
    QPalette contentPal = contentWidget->palette();
    contentPal.setColor(QPalette::Window, Qt::white);
    contentWidget->setPalette(contentPal);

    mainLayout = new QVBoxLayout(contentWidget);
    mainLayout->setSpacing(15);                     // Increase spacing between sections
    mainLayout->setContentsMargins(20, 20, 20, 20); // Add margins for better visual spacing

    // Initialize all UI sections
    createProjectInfoSection();
    createEnvironmentSection();
    createCalDevicesSection();
    createMeasurementsSection();

    // Add some spacing at the end
    mainLayout->addStretch();

    // Set the content widget to the scroll area
    scrollArea->setWidget(contentWidget);

    // Add close button
    closeButton = new QPushButton("关闭", this);
    closeButton->setMinimumWidth(100);
    connect(closeButton, &QPushButton::clicked, this, &QDialog::accept);

    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    buttonLayout->addWidget(closeButton);
    buttonLayout->setContentsMargins(20, 10, 20, 10); // Add margins to button area

    // Main dialog layout
    QVBoxLayout *dialogLayout = new QVBoxLayout(this);
    dialogLayout->setContentsMargins(0, 0, 0, 0); // Remove margins to maximize space
    dialogLayout->addWidget(scrollArea);
    dialogLayout->addLayout(buttonLayout);

    setLayout(dialogLayout);

    // Apply better styling with white backgrounds
    QString style = R"(
        QDialog {
            background-color: white;
        }
        QGroupBox {
            font-weight: bold;
            border: 1px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 15px;
            background-color: white;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            background-color: white;
        }
        QTableWidget {
            gridline-color: #cccccc;
            background-color: white;
        }
        QScrollArea {
            border: none;
            background-color: white;
        }
    )";

    setStyleSheet(style);
}
ProjectDetailsDialog::~ProjectDetailsDialog()
{
    // All Qt objects will be automatically deleted
}

void ProjectDetailsDialog::createProjectInfoSection()
{
    projectInfoGroup = new QGroupBox("项目信息", this);
    projectInfoLayout = new QGridLayout(projectInfoGroup);
    projectInfoLayout->setVerticalSpacing(10);
    projectInfoLayout->setHorizontalSpacing(20);

    // Add labels with better formatting
    QFont boldFont;
    boldFont.setBold(true);

    QLabel *nameTitle = new QLabel("项目名称:", this);
    nameTitle->setFont(boldFont);
    QLabel *modelTitle = new QLabel("设备型号:", this);
    modelTitle->setFont(boldFont);
    QLabel *typeTitle = new QLabel("设备类型:", this);
    typeTitle->setFont(boldFont);
    QLabel *serialTitle = new QLabel("设备序列号:", this);
    serialTitle->setFont(boldFont);
    QLabel *dateTitle = new QLabel("校准日期:", this);
    dateTitle->setFont(boldFont);

    projectInfoLayout->addWidget(nameTitle, 0, 0);
    projectInfoLayout->addWidget(modelTitle, 1, 0);
    projectInfoLayout->addWidget(typeTitle, 2, 0);
    projectInfoLayout->addWidget(serialTitle, 3, 0);
    projectInfoLayout->addWidget(dateTitle, 4, 0);

    // Add value labels with empty text initially
    for (int i = 0; i < 5; i++)
    {
        QLabel *valueLabel = new QLabel("", this);
        valueLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
        projectInfoLayout->addWidget(valueLabel, i, 1);
    }

    // Set column stretch to make value labels expand
    projectInfoLayout->setColumnStretch(1, 1);

    projectInfoGroup->setLayout(projectInfoLayout);
    mainLayout->addWidget(projectInfoGroup);
}

void ProjectDetailsDialog::createEnvironmentSection()
{
    environmentGroup = new QGroupBox("环境信息", this);
    environmentLayout = new QGridLayout(environmentGroup);
    environmentLayout->setVerticalSpacing(10);
    environmentLayout->setHorizontalSpacing(20);

    // Add labels with better formatting
    QFont boldFont;
    boldFont.setBold(true);

    QLabel *tempTitle = new QLabel("环境温度:", this);
    tempTitle->setFont(boldFont);
    QLabel *humTitle = new QLabel("环境湿度:", this);
    humTitle->setFont(boldFont);
    QLabel *preTitle = new QLabel("气压:", this);
    preTitle->setFont(boldFont);
    QLabel *calibratorTitle = new QLabel("校准员:", this);
    calibratorTitle->setFont(boldFont);
    QLabel *reportTitle = new QLabel("报告编号:", this);
    reportTitle->setFont(boldFont);

    environmentLayout->addWidget(tempTitle, 0, 0);
    environmentLayout->addWidget(humTitle, 1, 0);
    environmentLayout->addWidget(preTitle, 2, 0);
    environmentLayout->addWidget(calibratorTitle, 3, 0);
    environmentLayout->addWidget(reportTitle, 4, 0);

    // Add value labels with empty text initially
    for (int i = 0; i < 5; i++)
    {
        QLabel *valueLabel = new QLabel("", this);
        valueLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
        environmentLayout->addWidget(valueLabel, i, 1);
    }

    // Set column stretch to make value labels expand
    environmentLayout->setColumnStretch(1, 1);

    environmentGroup->setLayout(environmentLayout);
    mainLayout->addWidget(environmentGroup);
}

void ProjectDetailsDialog::createCalDevicesSection()
{
    devicesGroup = new QGroupBox("辅助校准设备", this);
    devicesLayout = new QGridLayout(devicesGroup);
    devicesLayout->setVerticalSpacing(10);
    devicesLayout->setHorizontalSpacing(20);

    // No need to pre-populate with labels as the number of devices is dynamic
    // We'll add them in fillCalDevicesInfo()

    devicesGroup->setLayout(devicesLayout);
    mainLayout->addWidget(devicesGroup);
}

void ProjectDetailsDialog::createMeasurementsSection()
{
    measurementsGroup = new QGroupBox("测量数据", this);
    QVBoxLayout *measurementsLayout = new QVBoxLayout(measurementsGroup);

    measurementsTable = new QTableWidget(this);
    measurementsTable->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    measurementsTable->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // Set a reasonable minimum size for the table
    measurementsTable->setMinimumHeight(400);

    measurementsLayout->addWidget(measurementsTable);
    measurementsGroup->setLayout(measurementsLayout);
    mainLayout->addWidget(measurementsGroup);

    // Make the measurements section take most of the available space
    measurementsGroup->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
}

void ProjectDetailsDialog::displayProjectData(const ProjectData &projectData)
{
    // Fill in all sections with project data
    fillProjectInfo(projectData);
    fillEnvironmentInfo(projectData.ambientInfo);
    fillCalDevicesInfo(projectData.calDeviceInfo);
    fillMeasurementsData(projectData);

    // After filling in all the data, resize the dialog to better fit the content
    adjustSize();
}

void ProjectDetailsDialog::fillProjectInfo(const ProjectData &projectData)
{
    // Update the value labels with project data
    QGridLayout *layout = static_cast<QGridLayout *>(projectInfoGroup->layout());

    // Get labels at position (row, 1) and update text
    QLabel *nameLabel = static_cast<QLabel *>(layout->itemAtPosition(0, 1)->widget());
    QLabel *modelLabel = static_cast<QLabel *>(layout->itemAtPosition(1, 1)->widget());
    QLabel *typeLabel = static_cast<QLabel *>(layout->itemAtPosition(2, 1)->widget());
    QLabel *serialLabel = static_cast<QLabel *>(layout->itemAtPosition(3, 1)->widget());
    QLabel *dateLabel = static_cast<QLabel *>(layout->itemAtPosition(4, 1)->widget());

    nameLabel->setText(projectData.projectName);
    modelLabel->setText(projectData.deviceModel);
    typeLabel->setText(projectData.deviceType);
    serialLabel->setText(projectData.deviceSerialNumber);
    dateLabel->setText(projectData.calibrationDate.toString("yyyy-MM-dd HH:mm:ss"));
}

void ProjectDetailsDialog::fillEnvironmentInfo(const AmbientInfo &ambientInfo)
{
    // Update the value labels with environment data
    QGridLayout *layout = static_cast<QGridLayout *>(environmentGroup->layout());

    QLabel *tempLabel = static_cast<QLabel *>(layout->itemAtPosition(0, 1)->widget());
    QLabel *humLabel = static_cast<QLabel *>(layout->itemAtPosition(1, 1)->widget());
    QLabel *preLabel = static_cast<QLabel *>(layout->itemAtPosition(2, 1)->widget());
    QLabel *calibratorLabel = static_cast<QLabel *>(layout->itemAtPosition(3, 1)->widget());
    QLabel *reportLabel = static_cast<QLabel *>(layout->itemAtPosition(4, 1)->widget());

    tempLabel->setText(ambientInfo.temp + " °C");
    humLabel->setText(ambientInfo.hum + " %RH");
    preLabel->setText(ambientInfo.pre + " hPa");
    calibratorLabel->setText(ambientInfo.calibrator);
    reportLabel->setText(ambientInfo.reportNum);
}

void ProjectDetailsDialog::fillCalDevicesInfo(const QList<DeviceInfo> &devices)
{
    // Clear any existing items from the layout
    QLayoutItem *item;
    while ((item = devicesLayout->takeAt(0)) != nullptr)
    {
        if (item->widget())
        {
            delete item->widget();
        }
        delete item;
    }

    if (devices.isEmpty())
    {
        // Add a "No devices" label if there are no devices
        QLabel *noDevicesLabel = new QLabel("无辅助校准设备", this);
        noDevicesLabel->setAlignment(Qt::AlignCenter);
        devicesLayout->addWidget(noDevicesLabel, 0, 0, 1, 4);
        return;
    }

    // Create header row with bold font
    QFont boldFont;
    boldFont.setBold(true);

    QLabel *typeHeader = new QLabel("设备类型", this);
    QLabel *modelHeader = new QLabel("型号", this);
    QLabel *serialHeader = new QLabel("序列号", this);
    QLabel *dateHeader = new QLabel("校准日期", this);

    typeHeader->setFont(boldFont);
    modelHeader->setFont(boldFont);
    serialHeader->setFont(boldFont);
    dateHeader->setFont(boldFont);

    typeHeader->setAlignment(Qt::AlignCenter);
    modelHeader->setAlignment(Qt::AlignCenter);
    serialHeader->setAlignment(Qt::AlignCenter);
    dateHeader->setAlignment(Qt::AlignCenter);

    // Add headers to the first row
    devicesLayout->addWidget(typeHeader, 0, 0);
    devicesLayout->addWidget(modelHeader, 0, 1);
    devicesLayout->addWidget(serialHeader, 0, 2);
    devicesLayout->addWidget(dateHeader, 0, 3);

    // Add a separator line
    QFrame *line = new QFrame(this);
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    devicesLayout->addWidget(line, 1, 0, 1, 4);

    // Add device data - use a separate row counter to properly handle separators
    int currentRow = 2; // Start after header and separator

    for (int i = 0; i < devices.size(); ++i)
    {
        const DeviceInfo &device = devices[i];

        QLabel *typeLabel = new QLabel(device.type, this);
        QLabel *modelLabel = new QLabel(device.model, this);
        QLabel *serialLabel = new QLabel(device.serialNumber, this);
        QLabel *dateLabel = new QLabel(device.calibrationDate, this);

        typeLabel->setAlignment(Qt::AlignCenter);
        modelLabel->setAlignment(Qt::AlignCenter);
        serialLabel->setAlignment(Qt::AlignCenter);
        dateLabel->setAlignment(Qt::AlignCenter);

        // Make text selectable
        typeLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
        modelLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
        serialLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
        dateLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);

        // Add to grid layout using our current row counter
        devicesLayout->addWidget(typeLabel, currentRow, 0);
        devicesLayout->addWidget(modelLabel, currentRow, 1);
        devicesLayout->addWidget(serialLabel, currentRow, 2);
        devicesLayout->addWidget(dateLabel, currentRow, 3);

        currentRow++; // Move to next row

        // Add separator line after each device except the last one
        if (i < devices.size() - 1)
        {
            QFrame *deviceSeparator = new QFrame(this);
            deviceSeparator->setFrameShape(QFrame::HLine);
            deviceSeparator->setFrameShadow(QFrame::Sunken);
            devicesLayout->addWidget(deviceSeparator, currentRow, 0, 1, 4);
            currentRow++; // Move past the separator row
        }
    }

    // Set column stretches to ensure proper spacing
    for (int i = 0; i < 4; i++)
    {
        devicesLayout->setColumnStretch(i, 1);
    }
}

void ProjectDetailsDialog::fillMeasurementsData(const ProjectData &projectData)
{
    // Clear the table
    measurementsTable->clear();

    // 隐藏行号和列号
    measurementsTable->verticalHeader()->setVisible(false);
    measurementsTable->horizontalHeader()->setVisible(false);

    // 设置最小列宽
    measurementsTable->horizontalHeader()->setMinimumSectionSize(88);

    // 设置滚动条策略
    measurementsTable->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    measurementsTable->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 检测设备类型
    bool isTM18SeriesType = projectData.deviceModel.contains("TM18ND") || projectData.deviceModel.contains("TM18RD");
    bool isTC1618AType = (projectData.deviceType == "1618A-TC板卡"); // 1618A-TC板卡类型
    bool isSpecialType = isTM18SeriesType || isTC1618AType; // TM18系列或TC板卡都使用特殊格式
    
    const QVector<ReferenceData> &referenceValues = projectData.referenceValues;

    int rowsPerRefValue = isSpecialType ? 8 : 9; // TM18系列和TC板卡用8行，标准设备用9行
    measurementsTable->setRowCount(referenceValues.size() * rowsPerRefValue);
    if (referenceValues.isEmpty())
    {
        measurementsTable->setColumnCount(0);
        return;
    }
    int columnCount = referenceValues[0].channels.size() + 1;
    measurementsTable->setColumnCount(columnCount);

    for (int refIdx = 0; refIdx < referenceValues.size(); ++refIdx)
    {
        const ReferenceData &refData = referenceValues[refIdx];
        int startRow = refIdx * rowsPerRefValue;

        // 设置表头行
        QString headerRefUnit = isTC1618AType ? "参考电压(mV)" : "参考电阻(Ω)";
        QTableWidgetItem *headerRefItem = new QTableWidgetItem(headerRefUnit);
        headerRefItem->setTextAlignment(Qt::AlignCenter);
        headerRefItem->setBackground(QColor(240, 240, 240));
        measurementsTable->setItem(startRow, 0, headerRefItem);

        // 添加通道表头
        for (int ch = 0; ch < refData.channels.size(); ++ch)
        {
            QTableWidgetItem *headerChItem = new QTableWidgetItem(QString("CH%1").arg(ch + 1));
            headerChItem->setTextAlignment(Qt::AlignCenter);
            headerChItem->setBackground(QColor(240, 240, 240));
            measurementsTable->setItem(startRow, ch + 1, headerChItem);
        }

        // 填充参考阻值
        QTableWidgetItem *refValueItem = new QTableWidgetItem(QString::number(refData.referenceValue, 'f', 5));
        refValueItem->setTextAlignment(Qt::AlignCenter);
        measurementsTable->setItem(startRow + 1, 0, refValueItem);

        // 合并参考阻值单元格（纵向合并4行）
        measurementsTable->setSpan(startRow + 1, 0, 4, 1);

        // 为每个通道添加4行空白单元格
        for (int ch = 0; ch < refData.channels.size(); ++ch)
        {
            for (int i = 0; i < 4; ++i)
            {
                QTableWidgetItem *emptyItem = new QTableWidgetItem("");
                emptyItem->setTextAlignment(Qt::AlignCenter);
                measurementsTable->setItem(startRow + 1 + i, ch + 1, emptyItem);
            }
        }

        // 填充测量值行
        QString measuredUnit = isTC1618AType ? "测量电压值(mV)" : "测量阻值(Ω)";
        QTableWidgetItem *measuredResistanceLabel = new QTableWidgetItem(measuredUnit);
        measuredResistanceLabel->setTextAlignment(Qt::AlignCenter);
        measurementsTable->setItem(startRow + 5, 0, measuredResistanceLabel);

        // 为每个通道添加测量阻值单元格
        for (int ch = 0; ch < refData.channels.size(); ++ch)
        {
            // 填充measured_resistances的4个值
            for (int i = 0; i < 4; ++i)
            {
                QTableWidgetItem *measuredResistanceSubItem = new QTableWidgetItem(QString::number(refData.channels[ch].measuredResistances[i], 'f', 5));
                measuredResistanceSubItem->setTextAlignment(Qt::AlignCenter);
                measurementsTable->setItem(startRow + 1 + i, ch + 1, measuredResistanceSubItem);
            }

            QTableWidgetItem *measuredResistanceItem = new QTableWidgetItem(QString::number(refData.channels[ch].measuredResistance, 'f', 5));
            measuredResistanceItem->setTextAlignment(Qt::AlignCenter);
            measurementsTable->setItem(startRow + 5, ch + 1, measuredResistanceItem);
        }

        // 填充偏差行
        QString deviationUnit = isTC1618AType ? "偏差(mV)" : "偏差(Ω)";
        QTableWidgetItem *deviationLabel = new QTableWidgetItem(deviationUnit);
        deviationLabel->setTextAlignment(Qt::AlignCenter);
        measurementsTable->setItem(startRow + 6, 0, deviationLabel);

        // 为每个通道添加偏差单元格
        for (int ch = 0; ch < refData.channels.size(); ++ch)
        {
            QTableWidgetItem *deviationItem = new QTableWidgetItem(QString::number(refData.channels[ch].deviationFromReference, 'f', 5));
            deviationItem->setTextAlignment(Qt::AlignCenter);
            // 对于特殊类型设备（TM18系列或TC板卡），在偏差行显示颜色（因为没有等效温度偏差行）
            if (isSpecialType)
            {
                deviationItem->setForeground(refData.channels[ch].calibrationResult ? Qt::green : Qt::red);
            }
            measurementsTable->setItem(startRow + 6, ch + 1, deviationItem);
        }

        // 填充允差行
        QString toleranceUnit;
        if (isTC1618AType) {
            toleranceUnit = "允差(mV)";
        } else if (isTM18SeriesType) {
            toleranceUnit = "允差(Ω)";
        } else {
            toleranceUnit = "允差(mK)";
        }
        
        QTableWidgetItem *toleranceLabel = new QTableWidgetItem(toleranceUnit);
        toleranceLabel->setTextAlignment(Qt::AlignCenter);
        measurementsTable->setItem(startRow + 7, 0, toleranceLabel);

        if (isTC1618AType)
        {
            // TC板卡：允差不合并，每个通道显示自己的允差值
            for (int ch = 0; ch < refData.channels.size(); ++ch)
            {
                double tolerance = refData.channels[ch].allowedDeviation;
                QString toleranceFormat = QString::number(tolerance, 'f', 5);
                QTableWidgetItem *toleranceValue = new QTableWidgetItem(toleranceFormat);
                toleranceValue->setTextAlignment(Qt::AlignCenter);
                measurementsTable->setItem(startRow + 7, ch + 1, toleranceValue);
            }
        }
        else
        {
            // TM18系列和标准设备：允差合并显示
            double tolerance = refData.channels[0].allowedDeviation;
            QString toleranceFormat;
            if (isTM18SeriesType)
            {
                // 根据设备型号确定小数位数：TM18RD-P需要2位，TM18ND-P需要1位
                int precision = (projectData.deviceModel.contains("TM18RD")) ? 2 : 1;
                toleranceFormat = QString::number(tolerance, 'f', precision);
            }
            else
            {
                toleranceFormat = QString::number(tolerance);
            }

            QTableWidgetItem *toleranceValue = new QTableWidgetItem(toleranceFormat);
            toleranceValue->setTextAlignment(Qt::AlignCenter);
            measurementsTable->setItem(startRow + 7, 1, toleranceValue);

            // 合并允差值单元格（横向合并所有通道列）
            measurementsTable->setSpan(startRow + 7, 1, 1, refData.channels.size());
        }

        // 只有标准设备才填充等效温度偏差行（TM18系列和TC板卡都不显示）
        if (!isSpecialType)
        {
            // 填充等效温度偏差行
            QTableWidgetItem *equivalentTempDeviationLabel = new QTableWidgetItem("等效温度偏差(mK)");
            equivalentTempDeviationLabel->setTextAlignment(Qt::AlignCenter);
            measurementsTable->setItem(startRow + 8, 0, equivalentTempDeviationLabel);

            // 为每个通道添加等效温度偏差单元格
            for (int ch = 0; ch < refData.channels.size(); ++ch)
            {
                QTableWidgetItem *equivalentTempDeviationItem = new QTableWidgetItem(QString::number(refData.channels[ch].equivalentTempDeviation, 'f', 1));
                equivalentTempDeviationItem->setTextAlignment(Qt::AlignCenter);
                equivalentTempDeviationItem->setForeground(refData.channels[ch].calibrationResult ? Qt::green : Qt::red);
                measurementsTable->setItem(startRow + 8, ch + 1, equivalentTempDeviationItem);
            }
        }
    }

    // 设置表格列宽自适应
    measurementsTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 设置表格样式
    measurementsTable->setStyleSheet("QTableWidget { gridline-color: #CCCCCC; }");
    measurementsTable->setShowGrid(true);
}
