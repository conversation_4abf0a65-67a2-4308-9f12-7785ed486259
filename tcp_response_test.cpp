#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <iomanip>
#include <algorithm>

class TcpResponseTest
{
public:
    static std::string vectorToHexString(const std::vector<uint8_t> &data)
    {
        std::stringstream ss;
        for (size_t i = 0; i < data.size(); ++i)
        {
            if (i > 0)
                ss << " ";
            ss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(data[i]);
        }
        return ss.str();
    }

    static std::vector<uint8_t> hexStringToVector(const std::string &hexStr)
    {
        std::string cleanHex = hexStr;
        cleanHex.erase(std::remove(cleanHex.begin(), cleanHex.end(), ' '), cleanHex.end());

        std::vector<uint8_t> result;
        for (size_t i = 0; i < cleanHex.length(); i += 2)
        {
            std::string byteString = cleanHex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
            result.push_back(byte);
        }
        return result;
    }

    static std::vector<uint8_t> processTcpResponse(const std::vector<uint8_t> &data)
    {
        std::vector<uint8_t> processedData = data;

        // 检查是否为TCP格式
        if (data.size() > 6 && data[0] == 0x00 && data[1] == 0x00 &&
            data[2] == 0x00 && data[3] == 0x00)
        {
            // TCP格式：解析TCP帧头部
            // 字节4-5：长度字段（大端格式）
            uint16_t length = (static_cast<uint16_t>(data[4]) << 8) | static_cast<uint16_t>(data[5]);

            std::cout << "检测到TCP帧，长度字段: " << length << std::endl;

            // 验证数据长度是否匹配
            if (data.size() >= 6 + length)
            {
                // 提取TCP帧中的RTU数据部分（去掉6字节TCP头部）
                processedData = std::vector<uint8_t>(data.begin() + 6, data.begin() + 6 + length);
                std::cout << "提取RTU数据成功" << std::endl;
            }
            else
            {
                // 长度不匹配，可能是损坏的TCP帧，保持原数据
                std::cout << "TCP帧长度不匹配，保持原数据" << std::endl;
                processedData = data;
            }
        }
        else
        {
            std::cout << "检测到RTU格式，保持原数据" << std::endl;
        }

        return processedData;
    }

    static void testTcpResponse()
    {
        std::cout << "=== TCP协议返回数据处理测试 ===" << std::endl;

        // 测试用例1：TCP返回数据
        std::cout << "\n--- 测试1：TCP返回数据 ---" << std::endl;
        std::string tcpResponseHex = "00 00 00 00 00 0D 01 03 0A 44 41 51 32 33 41 30 30 34 33";
        auto tcpResponse = hexStringToVector(tcpResponseHex);

        std::cout << "原始TCP数据: " << vectorToHexString(tcpResponse) << std::endl;

        auto processedTcp = processTcpResponse(tcpResponse);
        std::cout << "处理后数据: " << vectorToHexString(processedTcp) << std::endl;
        std::cout << "预期RTU数据: 01 03 0A 44 41 51 32 33 41 30 30 34 33" << std::endl;

        // 验证结果
        std::vector<uint8_t> expectedRtu = hexStringToVector("01 03 0A 44 41 51 32 33 41 30 30 34 33");
        bool tcpPassed = (processedTcp == expectedRtu);
        std::cout << "TCP测试结果: " << (tcpPassed ? "通过" : "失败") << std::endl;

        // 测试用例2：RTU返回数据
        std::cout << "\n--- 测试2：RTU返回数据 ---" << std::endl;
        std::string rtuResponseHex = "01 03 0A 44 41 51 32 33 41 30 30 34 33 EC C1";
        auto rtuResponse = hexStringToVector(rtuResponseHex);

        std::cout << "原始RTU数据: " << vectorToHexString(rtuResponse) << std::endl;

        auto processedRtu = processTcpResponse(rtuResponse);
        std::cout << "处理后数据: " << vectorToHexString(processedRtu) << std::endl;
        std::cout << "预期数据: " << rtuResponseHex << " (保持不变)" << std::endl;

        // 验证结果
        bool rtuPassed = (processedRtu == rtuResponse);
        std::cout << "RTU测试结果: " << (rtuPassed ? "通过" : "失败") << std::endl;

        // 测试用例3：短TCP帧（读取命令）
        std::cout << "\n--- 测试3：短TCP帧 ---" << std::endl;
        std::string shortTcpHex = "00 00 00 00 00 06 01 03 00 11 00 05";
        auto shortTcp = hexStringToVector(shortTcpHex);

        std::cout << "原始TCP数据: " << vectorToHexString(shortTcp) << std::endl;

        auto processedShort = processTcpResponse(shortTcp);
        std::cout << "处理后数据: " << vectorToHexString(processedShort) << std::endl;
        std::cout << "预期RTU数据: 01 03 00 11 00 05" << std::endl;

        // 验证结果
        std::vector<uint8_t> expectedShortRtu = hexStringToVector("01 03 00 11 00 05");
        bool shortPassed = (processedShort == expectedShortRtu);
        std::cout << "短TCP测试结果: " << (shortPassed ? "通过" : "失败") << std::endl;

        // 测试用例4：损坏的TCP帧
        std::cout << "\n--- 测试4：损坏的TCP帧 ---" << std::endl;
        std::string corruptedTcpHex = "00 00 00 00 00 FF 01 03"; // 长度字段错误
        auto corruptedTcp = hexStringToVector(corruptedTcpHex);

        std::cout << "损坏TCP数据: " << vectorToHexString(corruptedTcp) << std::endl;

        auto processedCorrupted = processTcpResponse(corruptedTcp);
        std::cout << "处理后数据: " << vectorToHexString(processedCorrupted) << std::endl;
        std::cout << "预期: 保持原数据不变" << std::endl;

        // 验证结果
        bool corruptedPassed = (processedCorrupted == corruptedTcp);
        std::cout << "损坏帧测试结果: " << (corruptedPassed ? "通过" : "失败") << std::endl;

        std::cout << "\n=== 测试完成 ===" << std::endl;
    }
};

int main()
{
    TcpResponseTest::testTcpResponse();
    return 0;
}
