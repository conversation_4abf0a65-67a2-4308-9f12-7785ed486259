#include "VoltageCalibrationWorker.h"
#include <QApplication>
#include <QThread>
#include <cmath>

// 静态常量定义
const QVector<VoltageCalibrationWorker::VoltageLevel> VoltageCalibrationWorker::VOLTAGE_LEVELS = {
    {0.0, "0mV"},
    {50.0, "50mV"}};

const double VoltageCalibrationWorker::VOLTAGE_THRESHOLD = 0.01; // 10μV = 0.01mV

VoltageCalibrationWorker::VoltageCalibrationWorker(QObject *parent)
    : QObject(parent),
      m_abortRequested(false), m_dataCollectionTimer(new QTimer(this)), m_currentLevel(0), m_currentAttempt(0), m_currentRound(0), m_calCommandHandler(nullptr)
{
    m_dataCollectionTimer->setSingleShot(true);
    connect(m_dataCollectionTimer, &QTimer::timeout, this, &VoltageCalibrationWorker::collectVoltageData);
}

VoltageCalibrationWorker::~VoltageCalibrationWorker()
{
}

void VoltageCalibrationWorker::setDeviceConfig(const CalDeviceConfig &config)
{
    m_deviceConfig = config;
}

void VoltageCalibrationWorker::setCommandHandlers(CommandHandlerFunc calHandler)
{
    m_calCommandHandler = calHandler;
}

void VoltageCalibrationWorker::requestAbort()
{
    m_abortRequested = true;
    if (m_dataCollectionTimer->isActive())
    {
        m_dataCollectionTimer->stop();
    }

    emit logMessage("电压校准被用户中止");
    emit voltageCalibrationFinished(false);
}

void VoltageCalibrationWorker::startVoltageCalibration()
{
    emit logMessage("开始TC类型电压校准...");
    emit logMessage(QString("设备型号: %1").arg(m_deviceConfig.name));

    // 清空表格数据
    emit clearVoltageTable();

    m_abortRequested = false;
    m_currentLevel = 0;
    m_currentAttempt = 1;

    // 初始进度条：0%开始
    emit voltageCalibrationProgress(0, 100);

    // 开始第一个电压档位的校准
    showUserPrompt(VOLTAGE_LEVELS[m_currentLevel].targetVoltage);
}

void VoltageCalibrationWorker::showUserPrompt(double targetVoltage)
{
    if (m_abortRequested)
        return;

    QString message = QString("请将754设备设置输出%1mV").arg(targetVoltage);
    emit logMessage(QString("等待用户设置754设备输出%1mV...").arg(targetVoltage));

    // 发送信号到主线程显示对话框
    emit requestUserPrompt(message);
}

void VoltageCalibrationWorker::onUserPromptResult(bool confirmed)
{
    if (m_abortRequested)
        return;

    if (confirmed)
    {
        double targetVoltage = VOLTAGE_LEVELS[m_currentLevel].targetVoltage;
        emit logMessage(QString("用户确认设置完成，开始写入校准值%1mV...").arg(targetVoltage));

        // 写入校准值
        if (writeVoltageCalibrationValue(targetVoltage))
        {
            emit logMessage("校准值写入成功，等待设备稳定...");

            // 等待2秒后开始数据采集
            QTimer::singleShot(2000, this, &VoltageCalibrationWorker::startDataCollection);
        }
        else
        {
            emit logMessage("校准值写入失败，重试当前档位...");

            // 重试当前档位
            if (m_currentAttempt < MAX_ATTEMPTS)
            {
                m_currentAttempt++;
                emit logMessage(QString("第%1次尝试校准%2档位...").arg(m_currentAttempt).arg(VOLTAGE_LEVELS[m_currentLevel].description));
                showUserPrompt(targetVoltage);
            }
            else
            {
                emit logMessage(QString("%1档位校准失败，已达到最大尝试次数").arg(VOLTAGE_LEVELS[m_currentLevel].description));
                finishCalibration(false);
            }
        }
    }
    else
    {
        emit logMessage("用户取消电压校准");
        finishCalibration(false);
    }
}

bool VoltageCalibrationWorker::writeVoltageCalibrationValue(double voltage)
{
    if (m_abortRequested)
        return false;

    // 获取写入地址
    quint16 writeAddr = getWriteAddress();

    // 创建写入命令
    QByteArray frame = createModbusWriteFrame(0x01, writeAddr, 0, voltage); // 通道1 (索引0)
    auto result = sendCommand(createModbusCommand(QString::fromLatin1(frame.toHex())), "Cal_WriteVoltageCalParams");

    if (result.first)
    {
        emit logMessage(QString("成功写入电压校准值: %1mV").arg(voltage));
        return true;
    }
    else
    {
        emit logMessage(QString("写入电压校准值失败: %1").arg(result.second));
        return false;
    }
}

void VoltageCalibrationWorker::startDataCollection()
{
    if (m_abortRequested)
        return;

    m_currentRound = 0;
    m_voltageReadings.clear();
    m_deviations.clear();

    emit logMessage("开始数据采集，将连续读取10次电压值...");

    // 开始第一次数据采集
    collectVoltageData();
}

void VoltageCalibrationWorker::collectVoltageData()
{
    if (m_abortRequested)
        return;

    if (m_currentRound >= MAX_ROUNDS)
    {
        // 数据采集完成，判断是否合格
        bool passed = isLevelPassed(m_deviations);

        // 获取最后一次的测量值和偏差
        double finalVoltage = m_voltageReadings.isEmpty() ? 0.0 : m_voltageReadings.last();
        double finalDeviation = m_deviations.isEmpty() ? 0.0 : m_deviations.last();

        // 发送档位完成信号，更新表格
        emit voltageLevelCompleted(m_currentLevel, finalVoltage, finalDeviation, passed);

        if (passed)
        {
            emit logMessage(QString("%1档位校准成功！").arg(VOLTAGE_LEVELS[m_currentLevel].description));

            // 更新进度条：0mV完成后50%，50mV完成后100%
            if (m_currentLevel == 0)
            {
                emit voltageCalibrationProgress(50, 100); // 0mV完成，50%进度
            }
            else if (m_currentLevel == 1)
            {
                emit voltageCalibrationProgress(100, 100); // 50mV完成，100%进度
            }

            moveToNextLevel();
        }
        else
        {
            emit logMessage(QString("%1档位校准失败，重试...").arg(VOLTAGE_LEVELS[m_currentLevel].description));

            if (m_currentAttempt < MAX_ATTEMPTS)
            {
                m_currentAttempt++;
                emit logMessage(QString("第%1次尝试校准%2档位...").arg(m_currentAttempt).arg(VOLTAGE_LEVELS[m_currentLevel].description));
                showUserPrompt(VOLTAGE_LEVELS[m_currentLevel].targetVoltage);
            }
            else
            {
                emit logMessage(QString("%1档位校准失败，已达到最大尝试次数").arg(VOLTAGE_LEVELS[m_currentLevel].description));
                // 发送失败的档位完成信号
                emit voltageLevelCompleted(m_currentLevel, finalVoltage, finalDeviation, false);
                finishCalibration(false);
            }
        }
        return;
    }

    // 读取电压值
    double voltage = readVoltageValue();
    if (voltage == 0.0 && m_currentRound > 0)
    { // 第一次读取可能为0，后续不应该为0
        emit logMessage(QString("第%1轮读取电压值失败").arg(m_currentRound + 1));
        finishCalibration(false);
        return;
    }

    m_voltageReadings.append(voltage);

    // 计算偏差
    double targetVoltage = VOLTAGE_LEVELS[m_currentLevel].targetVoltage;
    double deviation = std::abs(voltage - targetVoltage);
    m_deviations.append(deviation);

    // 判断当前轮次是否合格
    bool roundPassed = (deviation <= VOLTAGE_THRESHOLD);

    // 发送更新信号
    emit updateVoltageData(voltage, deviation, roundPassed);

    emit logMessage(QString("第%1轮: 电压=%2mV, 偏差=%3mV, %4")
                        .arg(m_currentRound + 1)
                        .arg(voltage, 0, 'f', 6)
                        .arg(deviation, 0, 'f', 6)
                        .arg(roundPassed ? "合格" : "不合格"));

    m_currentRound++;

    // 1秒后进行下一轮采集
    m_dataCollectionTimer->start(1000);
}

bool VoltageCalibrationWorker::isLevelPassed(const QVector<double> &deviations)
{
    if (deviations.size() < 3)
        return false;

    // 检查最后3次数据是否都满足精度要求
    for (int i = deviations.size() - 3; i < deviations.size(); ++i)
    {
        if (deviations[i] > VOLTAGE_THRESHOLD)
        {
            return false;
        }
    }
    return true;
}

void VoltageCalibrationWorker::moveToNextLevel()
{
    m_currentLevel++;

    if (m_currentLevel >= VOLTAGE_LEVELS.size())
    {
        // 所有档位校准完成
        emit logMessage("所有电压档位校准完成！");
        finishCalibration(true);
    }
    else
    {
        // 开始下一个档位的校准
        m_currentAttempt = 1;
        emit logMessage(QString("开始校准%1档位...").arg(VOLTAGE_LEVELS[m_currentLevel].description));

        showUserPrompt(VOLTAGE_LEVELS[m_currentLevel].targetVoltage);
    }
}

void VoltageCalibrationWorker::finishCalibration(bool success)
{
    if (m_dataCollectionTimer->isActive())
    {
        m_dataCollectionTimer->stop();
    }

    QString result = success ? "电压校准成功完成" : "电压校准失败";
    emit logMessage(result);
    emit voltageCalibrationFinished(success);
}

quint16 VoltageCalibrationWorker::getWriteAddress() const
{
    // 根据设备型号返回对应的写入地址
    if (m_deviceConfig.name.contains("1618A-N"))
    {
        return 0x0441; // 1618A-N系列
    }
    else if (m_deviceConfig.name.contains("1618A-L"))
    {
        return 0x00F9; // 1618A-L系列
    }
    return 0x0441; // 默认使用1618A-N地址
}

quint16 VoltageCalibrationWorker::getReadAddress() const
{
    // 根据设备型号返回对应的读取地址
    if (m_deviceConfig.name.contains("1618A-L"))
    {
        return 0x015D; // 1618A-L系列
    }
    else
    {
        return 0x003D; // 1618A-N系列
    }
}

// 通信相关方法实现
QPair<bool, QString> VoltageCalibrationWorker::sendCommand(const QByteArray &command, const QString &commandId)
{
    // 调用MainWindow的命令处理函数
    if (m_calCommandHandler)
    {
        return m_calCommandHandler(command, commandId);
    }
    return {false, "命令处理函数未设置"};
}

QByteArray VoltageCalibrationWorker::createModbusCommand(const QString &hexString)
{
    // 创建基础RTU数据（不含CRC）
    QByteArray rtuData = createModbusRtuFrame(hexString);

    // 根据设备型号选择协议格式
    if (isTcpDevice(m_deviceConfig.name))
    {
        // TCP设备：创建TCP帧格式
        return createModbusTcpFrame(rtuData);
    }
    else
    {
        // RTU设备：添加CRC校验
        uint16_t crc = calculateCRC16(rtuData);
        rtuData.append(static_cast<char>(crc & 0xFF));
        rtuData.append(static_cast<char>((crc >> 8) & 0xFF));
        return rtuData;
    }
}

uint16_t VoltageCalibrationWorker::calculateCRC16(const QByteArray &data)
{
    uint16_t crc = 0xFFFF;
    for (char ch : data)
    {
        crc ^= static_cast<uint8_t>(ch);
        for (int i = 0; i < 8; ++i)
        {
            if (crc & 0x0001)
            {
                crc = (crc >> 1) ^ 0xA001;
            }
            else
            {
                crc = crc >> 1;
            }
        }
    }
    return crc;
}

// TCP协议支持函数实现
bool VoltageCalibrationWorker::isTcpDevice(const QString &deviceModel)
{
    // 定义使用TCP协议的设备型号列表
    QStringList tcpDevices = {"TM14ND-P-LAN"};
    return tcpDevices.contains(deviceModel);
}

QByteArray VoltageCalibrationWorker::createModbusTcpFrame(const QByteArray &rtuData)
{
    // TCP帧结构：事务处理标识(2字节) + 协议标识(2字节) + 长度(2字节) + 单元标识符 + 功能码 + 数据
    QByteArray tcpFrame;

    // 事务处理标识 (2字节) - 使用固定值0x0000
    tcpFrame.append(static_cast<char>(0x00));
    tcpFrame.append(static_cast<char>(0x00));

    // 协议标识 (2字节) - Modbus协议固定为0x0000
    tcpFrame.append(static_cast<char>(0x00));
    tcpFrame.append(static_cast<char>(0x00));

    // 长度字段 (2字节) - 后续字节数（不包括事务处理标识和协议标识）
    uint16_t length = rtuData.size();
    tcpFrame.append(static_cast<char>((length >> 8) & 0xFF)); // 长度高字节
    tcpFrame.append(static_cast<char>(length & 0xFF));        // 长度低字节

    // 添加RTU数据（不包括CRC）
    tcpFrame.append(rtuData);

    return tcpFrame;
}

QByteArray VoltageCalibrationWorker::createModbusRtuFrame(const QString &hexCommand)
{
    // 移除所有空格
    QString cleanCommand = hexCommand.simplified().remove(' ');

    // 将十六进制字符串转换为 QByteArray
    QByteArray cmd = QByteArray::fromHex(cleanCommand.toLatin1());

    return cmd; // 返回不带CRC的RTU数据
}

QByteArray VoltageCalibrationWorker::createModbusWriteFrame(quint8 deviceAddr, quint16 startAddr, int channel, double value)
{
    // 计算该通道的寄存器地址（每个通道占用 4 个寄存器）
    quint16 addr = startAddr + channel * 4;
    quint8 addr_high = (addr >> 8) & 0xFF; // 高字节
    quint8 addr_low = addr & 0xFF;         // 低字节

    // 将 double 类型转换为小端字节序的 8 字节数据
    QByteArray byteArray;
    QDataStream stream(&byteArray, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);
    stream << value;

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr)); // 设备地址
    frame.append(static_cast<char>(0x10));       // 功能码 10（写多个寄存器）
    frame.append(static_cast<char>(addr_high));  // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));   // 寄存器地址低字节
    frame.append(static_cast<char>(0x00));       // 寄存器数量高字节
    frame.append(static_cast<char>(0x04));       // 寄存器数量低字节（4个寄存器）
    frame.append(static_cast<char>(0x08));       // 字节数（8字节）
    frame.append(byteArray);                     // 数据

    return frame;
}

QByteArray VoltageCalibrationWorker::createModbusReadFrame(quint8 deviceAddr, quint16 startAddr, int channel, int count)
{
    // 计算起始寄存器地址
    quint16 addr = startAddr + channel * 4; // 每个通道占用4个寄存器
    quint8 addr_high = (addr >> 8) & 0xFF;  // 提取高字节
    quint8 addr_low = addr & 0xFF;          // 提取低字节

    // 计算需要读取的寄存器数量
    quint16 numRegisters = count * 4;                      // 每个通道4个寄存器
    quint8 numRegisters_high = (numRegisters >> 8) & 0xFF; // 提取高字节
    quint8 numRegisters_low = numRegisters & 0xFF;         // 提取低字节

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr));        // 设备地址
    frame.append(static_cast<char>(0x03));              // 功能码 03（读保持寄存器）
    frame.append(static_cast<char>(addr_high));         // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));          // 寄存器地址低字节
    frame.append(static_cast<char>(numRegisters_high)); // 寄存器数量高字节
    frame.append(static_cast<char>(numRegisters_low));  // 寄存器数量低字节

    return frame;
}

double VoltageCalibrationWorker::readVoltageValue()
{
    if (m_abortRequested)
        return 0.0;

    // 获取读取地址
    quint16 readAddr = getReadAddress();

    // 创建读取命令
    QByteArray frame = createModbusReadFrame(0x01, readAddr, 0, 1); // 通道1 (索引0)
    auto result = sendCommand(createModbusCommand(QString::fromLatin1(frame.toHex())), "Cal_ReadVoltageValue");

    if (result.first)
    {
        // 解析返回的电压值
        double voltage = result.second.toDouble();
        return voltage;
    }
    else
    {
        emit logMessage(QString("读取电压值失败: %1").arg(result.second));
        return 0.0;
    }
}
