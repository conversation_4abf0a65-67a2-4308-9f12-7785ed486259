#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <iomanip>
#include <algorithm>

class TcpProtocolTest
{
public:
    static bool isTcpDevice(const std::string &deviceModel)
    {
        return deviceModel == "TM14ND-P-LAN";
    }

    static std::vector<uint8_t> createModbusTcpFrame(const std::vector<uint8_t> &rtuData)
    {
        std::vector<uint8_t> tcpFrame;

        // 事务处理标识 (2字节) - 0x0000
        tcpFrame.push_back(0x00);
        tcpFrame.push_back(0x00);

        // 协议标识 (2字节) - 0x0000
        tcpFrame.push_back(0x00);
        tcpFrame.push_back(0x00);

        // 长度字段 (2字节)
        uint16_t length = rtuData.size();
        tcpFrame.push_back((length >> 8) & 0xFF);
        tcpFrame.push_back(length & 0xFF);

        // 添加RTU数据
        tcpFrame.insert(tcpFrame.end(), rtuData.begin(), rtuData.end());

        return tcpFrame;
    }

    static std::vector<uint8_t> hexStringToVector(const std::string &hexStr)
    {
        std::string cleanHex = hexStr;
        cleanHex.erase(std::remove(cleanHex.begin(), cleanHex.end(), ' '), cleanHex.end());

        std::vector<uint8_t> result;
        for (size_t i = 0; i < cleanHex.length(); i += 2)
        {
            std::string byteString = cleanHex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
            result.push_back(byte);
        }
        return result;
    }

    static std::string vectorToHexString(const std::vector<uint8_t> &data)
    {
        std::stringstream ss;
        for (size_t i = 0; i < data.size(); ++i)
        {
            if (i > 0)
                ss << " ";
            ss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(data[i]);
        }
        return ss.str();
    }

    static uint16_t calculateCRC16(const std::vector<uint8_t> &data)
    {
        uint16_t crc = 0xFFFF;
        for (uint8_t ch : data)
        {
            crc ^= ch;
            for (int i = 0; i < 8; ++i)
            {
                if (crc & 0x0001)
                {
                    crc = (crc >> 1) ^ 0xA001;
                }
                else
                {
                    crc = crc >> 1;
                }
            }
        }
        return crc;
    }

    static std::vector<uint8_t> createModbusCommand(const std::string &hexCommand, const std::string &deviceModel)
    {
        std::vector<uint8_t> rtuData = hexStringToVector(hexCommand);

        if (isTcpDevice(deviceModel))
        {
            return createModbusTcpFrame(rtuData);
        }
        else
        {
            uint16_t crc = calculateCRC16(rtuData);
            rtuData.push_back(crc & 0xFF);
            rtuData.push_back((crc >> 8) & 0xFF);
            return rtuData;
        }
    }

    static void runTests()
    {
        std::cout << "=== TCP协议转换测试 ===" << std::endl;

        // 测试1：RTU设备
        std::cout << "\n--- 测试RTU设备 ---" << std::endl;
        std::string rtuDevice = "TM14ND-P";
        std::string hexCmd = "01 03 00 11 00 05";

        auto rtuResult = createModbusCommand(hexCmd, rtuDevice);
        std::cout << "设备: " << rtuDevice << std::endl;
        std::cout << "输入: " << hexCmd << std::endl;
        std::cout << "输出: " << vectorToHexString(rtuResult) << std::endl;
        std::cout << "预期: 01 03 00 11 00 05 D5 CC" << std::endl;

        // 测试2：TCP设备
        std::cout << "\n--- 测试TCP设备 ---" << std::endl;
        std::string tcpDevice = "TM14ND-P-LAN";

        auto tcpResult = createModbusCommand(hexCmd, tcpDevice);
        std::cout << "设备: " << tcpDevice << std::endl;
        std::cout << "输入: " << hexCmd << std::endl;
        std::cout << "输出: " << vectorToHexString(tcpResult) << std::endl;
        std::cout << "预期: 00 00 00 00 00 06 01 03 00 11 00 05" << std::endl;

        // 验证TCP转换
        std::vector<uint8_t> expectedTcp = {0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x01, 0x03, 0x00, 0x11, 0x00, 0x05};
        bool tcpPassed = (tcpResult == expectedTcp);
        std::cout << "TCP测试结果: " << (tcpPassed ? "通过" : "失败") << std::endl;

        // 验证RTU转换
        std::vector<uint8_t> expectedRtu = {0x01, 0x03, 0x00, 0x11, 0x00, 0x05, 0xD5, 0xCC};
        bool rtuPassed = (rtuResult == expectedRtu);
        std::cout << "RTU测试结果: " << (rtuPassed ? "通过" : "失败") << std::endl;

        // 测试3：更多命令类型
        std::cout << "\n--- 测试更多命令类型 ---" << std::endl;

        struct TestCase
        {
            std::string description;
            std::string hexCommand;
            std::string expectedTcp;
        };

        std::vector<TestCase> testCases = {
            {"写入单个寄存器", "01 06 00 0A 00 01", "00 00 00 00 00 06 01 06 00 0A 00 01"},
            {"读取波特率", "01 03 00 02 00 01", "00 00 00 00 00 06 01 03 00 02 00 01"},
            {"读取滤波次数", "01 03 00 0A 00 01", "00 00 00 00 00 06 01 03 00 0A 00 01"}};

        for (const auto &testCase : testCases)
        {
            std::cout << "\n测试: " << testCase.description << std::endl;

            auto result = createModbusCommand(testCase.hexCommand, tcpDevice);
            auto expected = hexStringToVector(testCase.expectedTcp);

            std::cout << "输入: " << testCase.hexCommand << std::endl;
            std::cout << "输出: " << vectorToHexString(result) << std::endl;
            std::cout << "预期: " << testCase.expectedTcp << std::endl;

            bool passed = (result == expected);
            std::cout << "结果: " << (passed ? "通过" : "失败") << std::endl;
        }

        std::cout << "\n=== 测试完成 ===" << std::endl;
    }
};

int main()
{
    TcpProtocolTest::runTests();
    return 0;
}
